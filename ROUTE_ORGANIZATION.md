# Route Organization Structure

## Overview
The routes have been reorganized for better clarity and maintainability, separating dashboard routes from calendar/appointment routes and other functional areas.

## Laravel Routes (`routes/web.php`)

### 📊 Dashboard Routes
**Purpose**: Main dashboard pages and admin interfaces

| Route | Component | Description |
|-------|-----------|-------------|
| `/` | `Index` | Main Dashboard |
| `/salon-admin` | `Index` | Salon Admin Dashboard |
| `/user-admin` | `UserAdmin` | User Admin Dashboard |
| `/super-admin` | `SuperAdmin` | Super Admin Dashboard |
| `/analytics` | `Analytics` | Analytics & Reports |
| `/finance` | `Finance` | Financial Dashboard |

### 📅 Appointment & Calendar Routes
**Purpose**: Appointment management and calendar functionality

| Route | Component | Description |
|-------|-----------|-------------|
| `/appointment/view` | `Appointment/Appointment` | View Appointments |
| `/appointment/add` | `Appointment/AddAppointment` | Add New Appointment |
| `/appointment/cancellations` | `Appointment/Cancellations` | Manage Cancellations |
| `/appointment/calendar-view` | `Appointment/CalendarView` | Calendar View |

### 🔐 Authentication Routes
**Purpose**: User authentication and account management

| Route | Component | Description |
|-------|-----------|-------------|
| `/auth/login` | `Authentication/LoginBoxed` | Login Page |
| `/auth/register` | `Authentication/RegisterBoxed` | Registration Page |

### 👤 User Management Routes
**Purpose**: User profiles and account settings

| Route | Component | Description |
|-------|-----------|-------------|
| `/users/profile` | `Users/Profile` | User Profile |
| `/users/account-setting` | `Users/AccountSetting` | Account Settings |

### 🛠️ Application Routes
**Purpose**: Main application features and tools

| Route | Component | Description |
|-------|-----------|-------------|
| `/apps/calendar` | `Apps/Calendar` | Calendar App |
| `/apps/chat` | `Apps/Chat` | Chat Application |
| `/apps/contacts` | `Apps/Contacts` | Contact Management |
| `/apps/invoice/list` | `Apps/Invoice/List` | Invoice List |
| `/apps/invoice/preview` | `Apps/Invoice/Preview` | Invoice Preview |
| `/apps/invoice/add` | `Apps/Invoice/Add` | Add Invoice |
| `/apps/invoice/edit` | `Apps/Invoice/Edit` | Edit Invoice |

## React Router Routes (`resources/js/src/router/routes.tsx`)

The React Router configuration has been organized to match the Laravel routes structure with the same categorization:

- **Dashboard Routes**: Main dashboards and admin interfaces
- **Appointment & Calendar Routes**: Appointment management and calendar views
- **Application Routes**: Main app features and tools
- **Additional Dashboard Pages**: Crypto, finance, and other specialized dashboards

## Benefits of This Organization

1. **Clear Separation of Concerns**: 
   - Dashboard routes are separate from appointment/calendar routes
   - Authentication routes are grouped together
   - Application features are organized by functionality

2. **Better Code Maintainability**:
   - Easy to find and modify specific route groups
   - Clear documentation with comments
   - Consistent naming conventions

3. **Improved Developer Experience**:
   - Routes are logically grouped and documented
   - Easy to understand the application structure
   - Consistent between Laravel and React Router configurations

4. **Scalability**:
   - Easy to add new routes to appropriate sections
   - Clear structure for future development
   - Organized for team collaboration

## Usage Examples

```php
// Dashboard routes
route('home')           // Main dashboard
route('user-admin')     // User admin dashboard
route('analytics')      // Analytics page

// Appointment routes
route('appointment.view')         // View appointments
route('appointment.add')          // Add appointment
route('appointment.calendar')     // Calendar view

// Application routes
route('apps.calendar')    // Calendar app
route('apps.invoice.list') // Invoice list
```

This organization makes it much easier to understand and maintain the routing structure of your application.
