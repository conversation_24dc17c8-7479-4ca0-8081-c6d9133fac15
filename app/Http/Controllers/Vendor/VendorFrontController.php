<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Mail\AppointmentBookedMail;
use App\Models\Appointment;
use App\Models\Branch;
use App\Models\BranchMedia;
use App\Models\PaymentMethod;
use App\Models\Plan;
use App\Models\PlanServiceUsage;
use App\Models\PlanUsage;
use App\Models\Service;
use App\Models\SubscriptionPlan;
use App\Models\Tenant;
use App\Models\User;
use App\Services\BranchService;
use App\Services\Notification\NotificationService;
use App\Services\VendorAppointmentService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class VendorFrontController extends Controller
{
    public function __construct(
        private readonly BranchService $branchDataService,
        private readonly VendorAppointmentService $appointmentService
    ) {}

    public function index(Request $request)
    {
        // Get tenant from current domain
        $tenantUrl = request()->getHost();
        $tenant    = Tenant::whereHas('domains', function ($query) use ($tenantUrl) {
            $query->where('domain', $tenantUrl);
        })->first();

        if (! $tenant) {
            // Show subscription plans to all users (including guests)
            $subscriptionPlans = SubscriptionPlan::active()
                ->ordered()
                ->get()
                ->map(function ($plan) {
                    return [
                        'id'              => $plan->id,
                        'name'            => $plan->name,
                        'description'     => $plan->description,
                        'price'           => $plan->price,
                        'currency_symbol' => $plan->currency_symbol,
                        'currency_code'   => $plan->currency_code,
                        'billing_cycle'   => $plan->billing_cycle,
                        'max_services'    => $plan->max_services,
                        'max_branches'    => $plan->max_branches,
                        'max_staff'       => $plan->max_staff,
                        'sort_order'      => $plan->sort_order,
                    ];
                });

            // Get active payment methods
            $paymentMethods = PaymentMethod::where('status', 'active')
                ->get()
                ->map(function ($method) {
                    return [
                        'id'   => $method->id,
                        'name' => $method->name,
                        'type' => $method->type,
                        'mode' => $method->mode,
                    ];
                });

            // Get current user info
            $authUser = Auth::user();

            // Check if user has an active subscription
            $existingActiveSubscription = null;
            if ($authUser) {
                $existingActiveSubscription = \App\Models\VendorSubscription::where('user_id', $authUser->id)
                    ->where('status', 'active')
                    ->where('ends_at', '>', now())
                    ->with('subscriptionPlan')
                    ->first();
            }

            return Inertia::render('Pages/AboutSalozy', [
                'layout'            => 'blank',
                'subscriptionPlans' => $subscriptionPlans,
                'paymentMethods'    => $paymentMethods,
                'authUser'          => $authUser ? [
                    'id'    => $authUser->id,
                    'name'  => $authUser->name,
                    'email' => $authUser->email,
                    'roles' => $authUser->getRoleNames(),
                ] : null,
                'hasActiveSubscription' => $existingActiveSubscription !== null,
                'activeSubscription'    => $existingActiveSubscription ? [
                    'id'             => $existingActiveSubscription->id,
                    'plan_name'      => $existingActiveSubscription->subscriptionPlan->name,
                    'ends_at'        => $existingActiveSubscription->ends_at->format('M d, Y'),
                    'days_remaining' => $existingActiveSubscription->daysUntilExpiry(),
                ] : null,
                'crmInfo' => [
                    'title'       => 'Welcome to Salozy CRM',
                    'description' => 'Salozy is a modern, all-in-one CRM platform designed for salons and service businesses. Manage appointments, staff, branches, services, and customer relationships with ease. Our intuitive dashboard, multi-branch support, and powerful analytics help you grow your business efficiently. Join Salozy.com today and experience seamless business management!',
                    'features'    => [
                        'Multi-branch management',
                        'Appointment scheduling',
                        'Staff and service management',
                        'Customer insights and analytics',
                        'Subscription plans for every business size',
                        'Gallery and slider for showcasing your work',
                        'Secure and scalable cloud platform',
                    ],
                    'cta' => 'Ready to elevate your salon business? Visit salozy.com and get started!',
                ],
            ]);
        }

        $user = User::where('tenant_id', $tenant->id)->first();
        if (! $user) {
            abort(404, 'User not found');
        }

        // Load relationships
        $user->load(['tenant.domains']);

        // Get all branches for this tenant through the user
        $branches = Branch::where('branches.user_id', $user->id)
            ->where('branches.is_active', true)
            ->join('working_hours', 'working_hours.branch_id', '=', 'branches.id')
            ->select('branches.*')
            ->get()
            ->map(function ($branch) {
                return [
                    'id'      => $branch->id,
                    'name'    => $branch->name,
                    'address' => $branch->address,
                ];
            });

        // Get selected branch ID from request or default to first branch
        $selectedBranchId = $request->input('branch_id', $branches->first()['id'] ?? null);

        // Get services and plans for the selected branch
        $branchData = $this->getBranchData($selectedBranchId, $user->id);

        // Get currency symbol and text from the branch
        $currency_symbol = '₹';
        $currency_text   = 'INR';
        $allow_staff     = false;
        $staffList       = [];
        if ($selectedBranchId) {
            $branch = Branch::find($selectedBranchId);
            if ($branch) {
                $currency_symbol = $branch->currency_symbol ?? '₹';
                $currency_text   = $branch->currency_text   ?? 'INR';
                $allow_staff     = $branch->allow_staff;
                if ($allow_staff) {
                    $staffList = User::role('staff')->where('branch_id', $selectedBranchId)->get(['id', 'name', 'email', 'profile']);
                }
            }
        }

        // Get slider and gallery images for the selected branch
        $sliderImages  = [];
        $galleryImages = [];
        if ($selectedBranchId) {
            $sliderImages = BranchMedia::where('branch_id', $selectedBranchId)
                ->where('type', 'slider')
                ->where('status', 1)
                ->orderByDesc('id')
                ->get(['id', 'image', 'type', 'status']);
            $galleryImages = BranchMedia::where('branch_id', $selectedBranchId)
                ->where('type', 'gallery')
                ->where('status', 1)
                ->orderByDesc('id')
                ->get(['id', 'image', 'type', 'status']);
        }

        // Get tenant data
        $tenantData = [
            'id'       => $tenant->id,
            'name'     => $user->name    ?? $tenant->id,
            'address'  => $user->address ?? '',
            'phone'    => $user->phone   ?? '',
            'email'    => $user->email   ?? '',
            'logo'     => $user->logo    ?? null,
            'images'   => $user->images  ?? [],
            'branches' => $branches,
        ];

        // Check if user is logged in
        $authUser = Auth::user();
        $authInfo = null;
        if ($authUser) {
            $authInfo = [
                'id'      => $authUser->id,
                'role'    => $authUser->roles->pluck('name')->toArray(),
                'name'    => $authUser->name,
                'email'   => $authUser->email,
                'profile' => $authUser->profile,
            ];
        }

        $workingHours = $this->branchDataService->getWorkingDataDetail($selectedBranchId);

        return Inertia::render('Vendor/FrontPage', [
            'tenant'                => $tenantData,
            'branchOpenData'        => $workingHours,
            'services'              => $branchData['services'],
            'plans'                 => $branchData['plans'],
            'current_branch_detail' => array_merge(
                is_array($branchData['branchdetail']) ? $branchData['branchdetail'] : $branchData['branchdetail']->toArray(),
                [
                    'currency_symbol' => $currency_symbol,
                    'currency_text'   => $currency_text,
                    'allow_staff'     => $allow_staff,
                    'staff'           => $staffList,
                ]
            ),
            'selectedBranchId' => $selectedBranchId,
            'sliderImages'     => $sliderImages,
            'galleryImages'    => $galleryImages,
            'layout'           => 'blank',
            'currency_symbol'  => $currency_symbol,
            'currency_text'    => $currency_text,
            'authUser'         => $authInfo,
        ]);
    }

    public function getBranchData($branchId, $userId)
    {
        // Verify branch belongs to the user
        $branch = Branch::where('id', $branchId)
            ->where('user_id', $userId)
            ->firstOrFail();

        if (! $branch) {
            return [
                'services' => [],
                'plans'    => [],
            ];
        }

        // Get services for the branch
        $services = Service::where('branch_id', $branchId)
            ->where('is_active', true)
            ->get()
            ->map(function ($service) {
                return [
                    'id'                     => $service->id,
                    'name'                   => $service->name,
                    'gender'                 => $service->gender,
                    'reminder_after_service' => $service->reminder_after_service,
                    'duration_minutes'       => $service->duration_minutes,
                    'price'                  => (float) $service->price,
                    'description'            => $service->description,
                    'image'                  => $service->image,
                ];
            });

        // Get plans for the branch
        $plans = Plan::where('branch_id', $branchId)
            ->where('status', 'active')
            ->with(['services' => function ($query) {
                $query->select('services.id', 'services.name')
                    ->withPivot('allowed_count');
            }])
            ->get()
            ->map(function ($plan) {
                return [
                    'id'            => $plan->id,
                    'name'          => $plan->name,
                    'description'   => $plan->description,
                    'price'         => (float) $plan->price,
                    'validity_days' => $plan->validity_days,
                    'status'        => $plan->status,
                    'services'      => $plan->services->map(function ($service) {
                        return [
                            'id'            => $service->id,
                            'name'          => $service->name,
                            'allowed_count' => $service->pivot->allowed_count,
                        ];
                    }),
                ];
            });

        return [
            'services'     => $services,
            'plans'        => $plans,
            'branchdetail' => $branch,
        ];
    }

    public function book(Request $request)
    {

        if (! Auth::check()) {

            $validated = $request->validate([
                'name'             => 'required|string|max:255',
                'email'            => 'required|email|max:255',
                'phone'            => 'required|string|max:20',
                'appointment_date' => 'required|date|after_or_equal:today',
                'appointment_time' => 'required|date_format:h:i A',
                'services'         => 'required|array|min:1',
                'services.*.id'    => 'required|exists:services,id',
                'password'         => 'required_if:is_new_user,true|min:8',
                'notes'            => 'nullable|string|max:500',
                'staff_id'         => 'nullable|integer',
                'branch_id'        => 'required|exists:branches,id',
                'anniversary'      => 'nullable|date',
                'date_of_birth'    => 'nullable|date',
            ]);

        } else {
            $validated = $request->validate([
                'appointment_date' => 'required|date|after_or_equal:today',
                'appointment_time' => 'required|date_format:h:i A',
                'notes'            => 'nullable|string|max:500',
                'branch_id'        => 'required|exists:branches,id',
                'services'         => 'required|array|min:1',
                'staff_id'         => 'nullable|integer',
                'services.*.id'    => 'required|exists:services,id',
            ]);
        }

        $tenantUrl = request()->getHost();
        $tenant    = Tenant::whereHas('domains', function ($query) use ($tenantUrl) {
            $query->where('domain', $tenantUrl);
        })->first();

        if (! $tenant) {
            return redirect()->back()->with('error', 'Tenant not found');
        }

        $user = User::where('tenant_id', $tenant->id)->first();
        if (! $user) {
            return redirect()->back()->with('error', 'User not found');
        }

        $branch = Branch::where('id', $validated['branch_id'])
            ->where('user_id', $user->id)
            ->first();

        if (! $branch) {
            return redirect()->back()->with('error', 'Invalid branch selected');
        }

        if (! Auth::check()) {

            $customer = User::where('email', $validated['email'])->first();

            if (! $customer) {
                $customer = User::create([
                    'name'          => $validated['name'],
                    'email'         => $validated['email'],
                    'phone'         => $validated['phone'],
                    'password'      => bcrypt($validated['password']),
                    'tenant_id'     => $tenant->id,
                    'anniversary'   => $validated['anniversary']   ?? null,
                    'date_of_birth' => $validated['date_of_birth'] ?? null,
                ]);
            }

        } else {
            $customer = User::find(Auth::id());
        }

        if (Auth::check()) {
            if ($branch->user_id == $customer->id) {
                return back()->withErrors(['error' => 'You can not send appoinment request on your own service']);
            }
        }

        $customer->assignRole('customer');

        $ticketNumber = 'TKT-'.strtoupper(substr(md5(uniqid()), 0, 8));

        // Prepare staff fields
        $staffId   = $validated['staff_id'] ?? null;
        $staffJson = null;
        if ($staffId) {
            $staffUser = User::find($staffId);
            if ($staffUser) {
                $staffJson = json_encode([
                    'id'    => $staffUser->id,
                    'name'  => $staffUser->name,
                    'email' => $staffUser->email,
                    'phone' => $staffUser->phone,
                ]);
            }

            if (! $branch->allow_staff) {
                return back()->withErrors(['error' => 'Your selected staff is not available.']);
            }

        }

        $validated['appointment_time'] = date('H:i', strtotime($validated['appointment_time']));

        $appointment = Appointment::create([
            'user_id'          => $customer->id,
            'tenant_id'        => $tenant->id,
            'branch_id'        => $validated['branch_id'],
            'appointment_date' => $validated['appointment_date'],
            'appointment_time' => $validated['appointment_time'],
            'currency_symbol'  => $branch->currency_symbol,
            'currency_text'    => $branch->currency_text,
            'ticket_number'    => $ticketNumber,
            'status'           => 'pending',
            'notes'            => $validated['notes'] ?? null,
            'staff_id'         => $staffId,
            'staff_json'       => $staffJson,
        ]);

        foreach ($validated['services'] as $service) {
            $serviceModel = Service::find($service['id']);

            $notification_date_after_service  = null;
            $notification_after_service_count = 0;

            $serviceData = Service::where('id', $service['id'])
                ->where('reminder_after_service', '!=', 0)
                ->where('total_repeat_service', '>', 0)
                ->first();

            if ($serviceData) {

                $appoinmentDate                   = $validated['appointment_date'];
                $reminder_after_service           = $serviceData->reminder_after_service;
                $totalRepeatCounter               = $serviceData->total_repeat_service;
                $notification_date_after_service  = Carbon::parse($appoinmentDate)->addDays($reminder_after_service);
                $notification_after_service_count = $totalRepeatCounter;

            }

            $appointment->services()->attach($service['id'], [
                'status'                           => 'pending',
                'price'                            => $serviceModel->price,
                'service_name'                     => $serviceModel->name,
                'notification_date_after_service'  => $notification_date_after_service,
                'notification_after_service_count' => $notification_after_service_count,
                'service_notes'                    => null,
            ]);
        }

        // Send email notifications to customer and vendor
        try {

            $vendorUser    = $user;
            $customerEmail = $customer->email;
            $vendorEmail   = $vendorUser->email;

            if ($branch->branch_user_id) {
                $branchuser  = User::find($branch->branch_user_id);
                $vendorEmail = $branchuser->email;
            }

            $branchInfo = $branch;
            $appointment->load('services');

            // To customer
            NotificationService::sendMail(
                $customerEmail,
                new AppointmentBookedMail($appointment, $customer, $branchInfo, $vendorUser, false)
            );
            // To vendor
            NotificationService::sendMail(
                $vendorEmail,
                new AppointmentBookedMail($appointment, $customer, $branchInfo, $vendorUser, true)
            );
        } catch (\Exception $e) {

            Log::error('Email sending failed: '.$e->getMessage(), [
                'exception'   => $e,
                'customer_to' => $customerEmail,
                'vendor_to'   => $vendorEmail,
                'appointment' => $appointment,
                'channel'     => $driver ?? 'smtp',
            ]);
        }

        if (! Auth::check()) {
            $centralDomain = config('services.central_domain.url');
            Auth::login($customer);

            $protocol         = $request->isSecure() ? 'https' : 'http';
            $section_title    = 'Redirecting to your dashboard';
            $section_subtitle = 'Please wait while we redirect you to your dashboard...';

            $redirectUrl = $customer->getRedirectUrl();

            if ($redirectUrl == '') {
                $redirectUrl = $protocol.'://'.$centralDomain.'/user/dashboard';
            }

            $redirectData = [
                'redirect_url'     => $redirectUrl,
                'section_title'    => $section_title,
                'section_subtitle' => $section_subtitle,
            ];
            session()->flash('user_redirect_data', $redirectData);

            return redirect()->route('vendor.redirect');
        }

        $this->appointmentService->updateAppoinmentServiceBeforeConfirm($appointment);

        return redirect()->back()->with('success', 'Appointment booked successfully. Please wait for approve. Your Ticket Number: '.$ticketNumber);
    }

    public function purchase(Request $request)
    {

        try {
            DB::beginTransaction();

            if (! Auth::check()) {

                $validated = $request->validate([
                    'name'          => 'required|string|max:255',
                    'email'         => 'required|email|max:255',
                    'phone'         => 'required|string|max:20',
                    'password'      => 'required|min:8',
                    'plan_id'       => 'required|exists:plans,id',
                    'branch_id'     => 'required|exists:branches,id',
                    'anniversary'   => 'nullable|date',
                    'date_of_birth' => 'nullable|date',
                ], [
                    'name.required'      => 'Please enter your full name',
                    'email.required'     => 'Please enter your email address',
                    'email.email'        => 'Please enter a valid email address',
                    'phone.required'     => 'Please enter your phone number',
                    'password.required'  => 'Please create a password',
                    'password.min'       => 'Password must be at least 8 characters',
                    'plan_id.required'   => 'Please select a plan',
                    'plan_id.exists'     => 'The selected plan is invalid',
                    'branch_id.required' => 'Please select a branch',
                    'branch_id.exists'   => 'The selected branch is invalid',
                ]);

            } else {

                $validated = $request->validate([
                    'plan_id'   => 'required|exists:plans,id',
                    'branch_id' => 'required|exists:branches,id',
                ], [
                    'plan_id.required'   => 'Please select a plan',
                    'plan_id.exists'     => 'The selected plan is invalid',
                    'branch_id.required' => 'Please select a branch',
                    'branch_id.exists'   => 'The selected branch is invalid',
                ]);

            }

            // Get the tenant from the current branch
            $tenant = Branch::find($validated['branch_id'])->user->tenant;

            if (! $tenant) {
                return back()->withErrors(['branch_id' => 'Tenant not found']);
            }

            $branchDetail = Branch::find($validated['branch_id']);

            // Get the plan and verify it belongs to the tenant
            $plan = Plan::with('services')
                ->whereHas('branch', function ($query) use ($tenant) {
                    $query->whereHas('user', function ($q) use ($tenant) {
                        $q->where('tenant_id', $tenant->id);
                    });
                })
                ->findOrFail($validated['plan_id']);

            if (! Auth::check()) {

                // Check if user exists
                $user = User::where('email', $validated['email'])->first();

                // If user doesn't exist, create new user
                if (! $user) {
                    try {
                        $user = User::create([
                            'name'          => $validated['name'],
                            'email'         => $validated['email'],
                            'phone'         => $validated['phone'],
                            'password'      => bcrypt($validated['password']),
                            'tenant_id'     => $tenant->id,
                            'anniversary'   => $validated['anniversary']   ?? null,
                            'date_of_birth' => $validated['date_of_birth'] ?? null,
                        ]);

                    } catch (Exception $ex) {
                        return back()->withErrors(['email' => 'Failed to create user: '.$ex->getMessage()]);
                    }
                }

            } else {
                $user = User::find(Auth::id());
            }

            if (Auth::check()) {
                if ($branchDetail->user_id == $user->id) {
                    return back()->withErrors(['error' => 'You can not purchase your own plan']);
                }
            }

            $user->assignRole('customer');

            // Create plan usage record
            $planUsage = PlanUsage::create([
                'user_id'         => $user->id,
                'plan_id'         => $plan->id,
                'branch_id'       => $validated['branch_id'],
                'currency_symbol' => $branchDetail->currency_symbol,
                'currency_text'   => $branchDetail->currency_text,
                'purchased_at'    => now(),
                'expires_at'      => now()->addDays($plan->validity_days),
                'status'          => 'pending',
            ]);

            // Create service usage records
            foreach ($plan->services as $service) {
                PlanServiceUsage::create([
                    'plan_usage_id'   => $planUsage->id,
                    'service_id'      => $service->id,
                    'service_name'    => $service->name,
                    'remaining_count' => $service->pivot->allowed_count,
                ]);
            }

            DB::commit();

            if (! Auth::check()) {
                $centralDomain = config('services.central_domain.url');
                Auth::login($user);

                $protocol         = $request->isSecure() ? 'https' : 'http';
                $section_title    = 'Redirecting to your dashboard';
                $section_subtitle = 'Please wait while we redirect you to your dashboard...';

                $redirectUrl = $user->getRedirectUrl();

                if ($redirectUrl == '') {
                    $redirectUrl = $protocol.'://'.$centralDomain.'/user/dashboard';
                }

                $redirectUrl  = $protocol.'://'.$centralDomain.'/user/dashboard';
                $redirectData = [
                    'redirect_url'     => $redirectUrl,
                    'section_title'    => $section_title,
                    'section_subtitle' => $section_subtitle,
                ];
                session()->flash('user_redirect_data', $redirectData);

                return redirect()->route('vendor.redirect');
            }

            return back()->with('success', 'Plan purchased successfully! Waiting for admin approval.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollBack();

            return back()->withErrors($e->errors());
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors([
                'name' => 'Failed to purchase plan: '.$e->getMessage(),
            ]);
        }
    }
}
