<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class ProfileController extends Controller
{
    public function show()
    {
        $user      = Auth::user();
        $userRoles = $user->getRoleNames();
        $userData  = $user->toArray();
        if ($userRoles->contains('customer')) {
            $userData['anniversary']   = $user->anniversary;
            $userData['date_of_birth'] = $user->date_of_birth;
        }

        return Inertia::render('Vendor/Profile/AccountSetting', [
            'user'      => $userData,
            'userRoles' => $userRoles,
        ]);
    }

    public function update(Request $request)
    {
        $user      = auth()->user();
        $userRoles = $user->getRoleNames();
        $rules     = [
            'name'    => 'required|string|max:255',
            'email'   => 'required|email|max:255|unique:users,email,'.$user->id,
            'phone'   => 'nullable|string|max:20',
            'gender'  => 'nullable|in:male,female,other',
            'address' => 'nullable|string|max:255',
            'logo'    => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'profile' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ];
        if ($userRoles->contains('customer')) {
            $rules['anniversary']   = 'nullable|date';
            $rules['date_of_birth'] = 'nullable|date';
        }
        $validated = $request->validate($rules);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($user->logo) {
                Storage::disk('custom_public')->delete($user->logo);
            }

            // Store new logo in tenant-specific storage
            $path              = $request->file('logo')->store('profile-logos', 'custom_public');
            $validated['logo'] = $path;
        }

        // Handle profile image upload
        if ($request->hasFile('profile')) {
            // Delete old profile image if exists
            if ($user->profile) {
                Storage::disk('custom_public')->delete($user->profile);
            }
            // Store new profile image in tenant-specific storage
            $profilePath          = $request->file('profile')->store('profile-images', 'custom_public');
            $validated['profile'] = $profilePath;
        }

        $user->fill($validated);
        $user->save();

        if ($userRoles->contains('customer')) {
            $user->anniversary   = $validated['anniversary']   ?? null;
            $user->date_of_birth = $validated['date_of_birth'] ?? null;
        }

        if ($request->wantsJson()) {
            return response()->json([
                'message' => 'Profile updated successfully',
                'user'    => $user,
            ]);
        }

        return redirect()->back()->with('success', 'Profile updated successfully');
    }

    public function updatePassword(Request $request)
    {
        $validated = $request->validate([
            'current_password' => 'required|current_password',
            'password'         => 'required|string|min:8|confirmed',
        ]);

        Auth::user()->update([
            'password' => Hash::make($validated['password']),
        ]);

        return back()->with('success', 'Password updated successfully');
    }
}
