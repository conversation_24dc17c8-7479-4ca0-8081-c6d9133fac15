<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Services\UserDashboardService;
use Illuminate\Support\Facades\Auth;

class UserDashboardController extends Controller
{
    public function __construct(private readonly UserDashboardService $userDashboardService) {}

    public function index()
    {
        $user          = Auth::user();
        $dashboardData = $this->userDashboardService->getDashboardData($user);

        // dd($dashboardData);
        return inertia('User/Dashboard', $dashboardData);
    }
}
