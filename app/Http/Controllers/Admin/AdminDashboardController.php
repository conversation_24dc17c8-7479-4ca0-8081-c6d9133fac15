<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Appointment;
use App\Models\Branch;
use App\Models\User;
use App\Services\Admin\AdminDashboardService;
use Illuminate\Support\Facades\Auth;

class AdminDashboardController extends Controller
{
    public function __construct(private readonly AdminDashboardService $adminDashboardService) {}

    public function index()
    {
        $user          = Auth::user();
        $dashboardData = $this->adminDashboardService->getDashboardData($user);
        // dd($dashboardData);

        //         $userdata = User::find(8);
        //         $customer = User::find(41);

        //         $branch = Branch::where('id', 2)
        //             ->where('user_id', $userdata->id)
        //             ->first();

        //             $appointment = Appointment::find(42);

        //         // Send email notifications to customer and vendor
        //         try {
        //             $vendorUser = $userdata;
        //             $customerEmail = $customer->email;
        //             $vendorEmail = $vendorUser->email;

        //             if($branch->branch_user_id){
        //                 $branchuser = User::find($branch->branch_user_id);
        //                 $vendorEmail = $branchuser->email;
        //             }

        //             $branchInfo = $branch;
        //             $appointment->load('services');

        //             // To customer
        //             \App\Services\Notification\NotificationService::sendMail(
        //                 $customerEmail,
        //                 new \App\Mail\AppointmentBookedMail($appointment, $customer, $branchInfo, $vendorUser, false)
        //             );
        //             // To vendor
        //             \App\Services\Notification\NotificationService::sendMail(
        //                 $vendorEmail,
        //                 new \App\Mail\AppointmentBookedMail($appointment, $customer, $branchInfo, $vendorUser, true)
        //             );

        // echo 'sent';
        //             exit;

        //         } catch (\Exception $e) {
        //             echo $e->getMessage();
        //             exit;
        //         }

        return inertia('Admin/Dashboard', $dashboardData);
    }
}
