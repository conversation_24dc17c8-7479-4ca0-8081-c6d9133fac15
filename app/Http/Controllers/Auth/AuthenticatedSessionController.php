<?php

declare(strict_types=1);

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;
use Stancl\Tenancy\Database\Models\Domain;

final class AuthenticatedSessionController extends Controller
{
    /**
     * Show the login page.
     */
    public function create(Request $request): Response
    {

        $currentDomain = $request->getHost();

        $centralDomains = config('tenancy.central_domains', []);

        // Check if current domain is a central domain
        if (! in_array($currentDomain, $centralDomains)) {
            $data['central_domain'] = false;
        } else {
            $data['central_domain'] = true;
        }

        $data['logo'] = url('logo.png');

        $checkDomain = Domain::where('domain', $currentDomain)->first();
        if ($checkDomain) {

            $checkUser = User::where('tenant_id', $checkDomain->tenant_id)->first();

            if ($checkUser->logo) {

                $fullPahtUrl  = $checkUser->logo;
                $data['logo'] = url($fullPahtUrl);
            } else {
                $data['logo'] = '';
            }
        }

        return Inertia::render('Authentication/LoginBoxed', [
            'canResetPassword' => Route::has('password.request'),
            'status'           => $request->session()->get('status'),
            'layout'           => 'blank',
            'data'             => $data,
        ]);
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();
        $centralDomain = config('services.central_domain.url');

        // Check user role and redirect accordingly
        $user     = $request->user();
        $protocol = $request->isSecure() ? 'https' : 'http';

        Auth::guard('web')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        $section_title    = 'Redirecting to your dashboard';
        $section_subtitle = 'Please wait while we redirect you to your dashboard...';

        $redirectUrl = $user->getRedirectUrl();

        if ($redirectUrl == '') {
            Auth::logout();

            return redirect()->back();
        }

        // If user is a vendor, redirect to their tenant domain
        // if ($user->hasRole('admin')) {
        //     $redirectUrl = $protocol.'://'.$centralDomain.'/siteadmin/dashboard';
        // } elseif ($user->hasRole('vendor') && $user->company_domain) {
        //     $redirectUrl = $protocol.'://'.$user->company_domain.'.'.$centralDomain.'/vendor/dashboard';
        // } elseif ($user->hasRole('staff') && $user->branch_id) {

        //     $branchUsers = Branch::findOrFail($user->branch_id);
        //     $parentUser  = User::findOrFail($branchUsers->user_id);
        //     if (! $user->tenant_id) {
        //         $user->tenant_id      = $parentUser->tenant_id;
        //         $user->company_name   = $parentUser->company_name;
        //         $user->parent_user_id = $parentUser->id;
        //         $user->save();
        //     }
        //     $redirectUrl = $protocol.'://'.$parentUser->company_domain.'.'.$centralDomain.'/own-appoinment';

        // } elseif ($user->hasRole('customer')) {
        //     $redirectUrl = $protocol.'://'.$centralDomain.'/user/dashboard';
        // } elseif ($user->hasRole('branchuser')) {
        //     $parentUser  = User::find($user->parent_user_id);
        //     $redirectUrl = $protocol.'://'.$parentUser->company_domain.'.'.$centralDomain.'/vendor/dashboard';
        // } else {
        //     Auth::logout();
        //     return redirect()->back();
        // }

        Auth::login($user);
        $redirectData = [
            'redirect_url'     => $redirectUrl,
            'section_title'    => $section_title,
            'section_subtitle' => $section_subtitle,
        ];
        session()->flash('user_redirect_data', $redirectData);

        return redirect()->route('vendor.redirect');

        // Only allow admin and customer access to central domain
        if ($user->hasRole('admin') || $user->hasRole('customer')) {
            Auth::guard('web')->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            $vendorUrl = $protocol.'://'.$centralDomain.'/admin/dashboard';
            Auth::login($user);

            return redirect()->route('vendor.redirect');
        }

        $request->session()->regenerate();

        // Check if user has a current_branch_id set
        if (! $user->current_branch_id) {
            // Get the first branch associated with this user
            $branch = Branch::first();
            if ($branch) {
                $user->current_branch_id = $branch->id;
                $user->save();
            }
        }

        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {

        if (session()->has('admin_user_id')) {
            $adminId = session()->pull('admin_user_id');
            $user    = User::find($adminId);
            if ($user && $user->hasRole('admin')) {

                Auth::guard('web')->logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                Auth::login($user);
                $redirectUrl = $user->getRedirectUrl();
                if (trim($redirectUrl) != '') {
                    session()->forget('admin_user_id');
                    $section_title    = 'Redirecting to your dashboard';
                    $section_subtitle = 'Please wait while we redirect you to your dashboard...';
                    $redirectData     = [
                        'redirect_url'     => $redirectUrl,
                        'section_title'    => $section_title,
                        'section_subtitle' => $section_subtitle,
                    ];
                    session()->flash('user_redirect_data', $redirectData);

                    return redirect()->route('vendor.redirect');

                }
            }
        }
        Auth::guard('web')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login');
    }
}
