<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Models\Branch;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureVendorHasBranch
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        // Only apply to authenticated vendors
        if ($user && $user->hasRole('vendor')) {
            try {
                // Check if user has a current_branch_id set
                if (! $user->current_branch_id) {
                    // Get the first branch owned by this vendor
                    $branch = Branch::where('user_id', $user->id)->first();

                    if (! $branch) {
                        // Create a default branch if none exists
                        $branch = Branch::create([
                            'user_id'   => $user->id,
                            'name'      => 'Main Branch',
                            'address'   => '',
                            'phone'     => '',
                            'is_active' => true,
                        ]);
                    }

                    // Set the current branch
                    $user->current_branch_id = $branch->id;
                    $user->save();
                }
            } catch (\Exception $e) {
                // Log the error but don't block the request
                \Log::error('EnsureVendorHasBranch middleware error: '.$e->getMessage());
            }
        }

        return $next($request);
    }
}
