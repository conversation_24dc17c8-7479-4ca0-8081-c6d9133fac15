<?php

namespace App\Http\Middleware;

use App\Models\Branch;
use Illuminate\Http\Request;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Defines the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     */
    public function share(Request $request): array
    {
        $centralDomains = config('tenancy.central_domains', []);
        $currentDomain  = $request->getHost();
        $isCentral      = true;
        if (! in_array($currentDomain, $centralDomains, true)) {
            $isCentral = false;
        }

        return array_merge(parent::share($request), [
            'auth' => [
                'user'  => $request->user()?->load('branch'),
                'roles' => $request->user() ? $request->user()->getRoleNames() : [],
            ],
            'site_branches' => $request->user() ? Branch::where('is_active', true)
                ->where('user_id', $request->user()->id)
                ->orderBy('created_at', 'desc')
                ->get()->map(function ($branch) {
                    return [
                        'id'        => $branch->id,
                        'name'      => $branch->name,
                        'logo'      => $branch->logo ? asset('storage/'.$branch->logo) : null,
                        'is_active' => $branch->is_active,
                    ];
                }) : collect(),
            'flash' => [
                'success' => fn () => $request->session()->get('success'),
                'error'   => fn () => $request->session()->get('error'),
                'message' => fn () => $request->session()->get('message'),
            ],
            'ziggy' => function () use ($request) {
                return [
                    'url'    => $request->url(),
                    'port'   => $request->getPort(),
                    'routes' => app('router')->getRoutes()->getRoutesByName(),
                ];
            },
            'is_central_domain' => $isCentral,
            'central_domains'   => $centralDomains,
        ]);
    }
}
