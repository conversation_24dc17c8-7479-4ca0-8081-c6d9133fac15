<?php

declare(strict_types=1);

namespace App\Services\Admin;

use App\Models\VendorSubscription;

class VendorSubscriptionService
{
    public function list()
    {
        return VendorSubscription::with(['user', 'subscriptionPlan'])
            ->orderByDesc('created_at')
            ->get();
    }

    public function find($id): ?VendorSubscription
    {
        return VendorSubscription::with(['user', 'subscriptionPlan'])->findOrFail($id);
    }

    public function update($id, array $data): bool
    {
        $subscription = VendorSubscription::findOrFail($id);

        return $subscription->update($data);
    }

    public function delete($id): bool
    {
        $subscription = VendorSubscription::findOrFail($id);

        return $subscription->delete();
    }

    public function trashed()
    {
        return VendorSubscription::onlyTrashed()
            ->with(['user', 'subscriptionPlan'])
            ->orderByDesc('deleted_at')
            ->get();
    }

    public function restore($id): bool
    {
        $subscription = VendorSubscription::onlyTrashed()->findOrFail($id);

        return $subscription->restore();
    }
}
