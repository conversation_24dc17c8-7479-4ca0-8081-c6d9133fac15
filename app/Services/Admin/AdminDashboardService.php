<?php

declare(strict_types=1);

namespace App\Services\Admin;

use App\Models\SubscriptionPlan;
use App\Models\User;
use App\Models\VendorSubscription;

class AdminDashboardService
{
    public function getDashboardData($user): array
    {
        // Total Registered Vendors (users with 'vendor' role)
        $totalVendors = User::whereHas('roles', function ($q) {
            $q->where('name', 'vendor');
        })->count();

        // Vendors without an active subscription
        $pendingVendors = User::whereHas('roles', function ($q) {
            $q->where('name', 'vendor');
        })->whereDoesntHave('vendorSubscriptions', function ($q) {
            $q->where('status', 'active')->where('ends_at', '>', now());
        })->count();

        // Total Subscription Plans
        $totalPlans = SubscriptionPlan::count();

        // Total Revenue (sum of all paid/active subscriptions)
        $totalRevenue = VendorSubscription::join('subscription_plans', 'vendor_subscriptions.subscription_plan_id', '=', 'subscription_plans.id')
            ->sum('subscription_plans.price');

        return [
            'totalVendors'   => $totalVendors,
            'pendingVendors' => $pendingVendors,
            'totalPlans'     => $totalPlans,
            'totalRevenue'   => $totalRevenue,
        ];
    }
}
