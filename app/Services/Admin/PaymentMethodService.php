<?php

declare(strict_types=1);

namespace App\Services\Admin;

use App\Models\PaymentMethod;

class PaymentMethodService
{
    public function list()
    {
        return PaymentMethod::all();
    }

    public function find($id): ?PaymentMethod
    {
        return PaymentMethod::findOrFail($id);
    }

    public function create(array $data): array
    {
        // Check if a soft-deleted record with the same type exists
        $existingTrashed = PaymentMethod::onlyTrashed()
            ->where('type', $data['type'])
            ->first();

        if ($existingTrashed) {
            // Restore and update the existing record
            $existingTrashed->restore();
            $existingTrashed->update($data);

            return [
                'model'        => $existingTrashed,
                'was_restored' => true,
            ];
        }

        return [
            'model'        => PaymentMethod::create($data),
            'was_restored' => false,
        ];
    }

    public function findByType(string $type, bool $includeTrashed = false): ?PaymentMethod
    {
        $query = PaymentMethod::where('type', $type);

        if ($includeTrashed) {
            $query->withTrashed();
        }

        return $query->first();
    }

    public function update($id, array $data): bool
    {
        $method = PaymentMethod::findOrFail($id);

        // If type is being changed, check for conflicts
        if (isset($data['type']) && $data['type'] !== $method->type) {
            // Check if a soft-deleted record with the new type exists
            $existingTrashed = PaymentMethod::onlyTrashed()
                ->where('type', $data['type'])
                ->first();

            if ($existingTrashed) {
                // Delete the current record and restore the trashed one with updated data
                $method->delete();
                $existingTrashed->restore();

                return $existingTrashed->update($data);
            }
        }

        return $method->update($data);
    }

    public function delete($id): bool
    {
        $method = PaymentMethod::findOrFail($id);

        return $method->delete();
    }

    public function trashed()
    {
        return PaymentMethod::onlyTrashed()->get();
    }

    public function restore($id): bool
    {
        $method = PaymentMethod::onlyTrashed()->findOrFail($id);

        return $method->restore();
    }
}
