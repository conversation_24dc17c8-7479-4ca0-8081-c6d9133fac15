<?php

declare(strict_types=1);

namespace App\Services;

use Carbon\Carbon;
use App\Models\Tenant;
use App\Models\Branch;
use Illuminate\Support\Facades\Request;

class UserDashboardService
{
    public function getDashboardData($user): array
    {
        $tenantUrl = request()->getHost();
        $tenant = Tenant::whereHas('domains', function($query) use ($tenantUrl) {
            $query->where('domain', $tenantUrl);
        })->first();
        if (!$tenant) {
            return [
                'totalAppointments' => 0,
                'totalPaid' => 0,
                'appointmentHistory' => [],
                'todaysAppointments' => [],
                'incomingAppointments' => [],
                'planHistory' => [],
                'activePlan' => null,
            ];
        }

        // Get all branches for this tenant
        $branches = Branch::whereHas('user', function($q) use ($tenant) {
            $q->where('tenant_id', $tenant->id);
        })->pluck('id');

        // Only allow if user is customer
        if (!$user->hasRole('customer')) {
            return [
                'totalAppointments' => 0,
                'totalPaid' => 0,
                'appointmentHistory' => [],
                'todaysAppointments' => [],
                'incomingAppointments' => [],
                'planHistory' => [],
                'activePlan' => null,
            ];
        }

        $today = now()->toDateString();

        // Total appointments for this user in this tenant's branches
        $totalAppointments = \App\Models\Appointment::where('user_id', $user->id)
            ->whereIn('branch_id', $branches)
            ->count();

        // Total paid (sum of all completed appointment services for the user in these branches)
        $totalPaid = \App\Models\AppointmentService::whereHas('appointment', function ($q) use ($user, $branches) {
            $q->where('user_id', $user->id)->whereIn('branch_id', $branches);
        })->where('status', 'completed')->sum('price');

        // Appointment history (all appointments for this user in these branches)
        $appointmentHistory = \App\Models\Appointment::with('branch')
            ->where('user_id', $user->id)
            ->whereIn('branch_id', $branches)
            ->orderByDesc('appointment_date')
            ->get()
            ->map(function ($a) {
                return [
                    'id' => $a->id,
                    'branch_name' => $a->branch->name ?? '',
                    'date' => $a->appointment_date ? $a->appointment_date->format('Y-m-d') : '',
                    'time' => $a->appointment_time,
                    'status' => $a->status,
                    'total_paid' => $a->appointmentServices->sum('price'),
                    'ticket_number' => $a->ticket_number,
                ];
            });

        // Today's appointments
        $todaysAppointments = $appointmentHistory->filter(fn($a) => $a['date'] === $today)->values();

        // Incoming appointments (future appointments, status pending or in_progress)
        $incomingAppointments = $appointmentHistory->filter(fn($a) => $a['date'] > $today && in_array($a['status'], ['pending', 'in_progress']))->values();

        // Plan history (for this user in these branches)
        $planHistory = \App\Models\PlanUsage::with('plan')
            ->where('user_id', $user->id)
            ->whereIn('branch_id', $branches)
            ->orderByDesc('purchased_at')
            ->get()
            ->map(function ($p) {
                return [
                    'id' => $p->id,
                    'name' => $p->plan->name ?? '',
                    'price' => $p->plan->price ?? 0,
                    'validity_days' => $p->plan->validity_days ?? 0,
                    'status' => $p->status,
                    'purchased_at' => $p->purchased_at ? $p->purchased_at->format('Y-m-d') : '',
                    'expires_at' => $p->expires_at ? $p->expires_at->format('Y-m-d') : '',
                ];
            });

        // Active plan (first active and not expired for this user in these branches)
        $activePlan = \App\Models\PlanUsage::with('plan')
            ->where('user_id', $user->id)
            ->whereIn('branch_id', $branches)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->orderByDesc('expires_at')
            ->first();
        $activePlanArr = $activePlan ? [
            'id' => $activePlan->id,
            'name' => $activePlan->plan->name ?? '',
            'price' => $activePlan->plan->price ?? 0,
            'validity_days' => $activePlan->plan->validity_days ?? 0,
            'expires_at' => $activePlan->expires_at ? $activePlan->expires_at->format('Y-m-d') : '',
        ] : null;

        return [
            'totalAppointments' => $totalAppointments,
            'totalPaid' => $totalPaid,
            'appointmentHistory' => $appointmentHistory,
            'todaysAppointments' => $todaysAppointments,
            'incomingAppointments' => $incomingAppointments,
            'planHistory' => $planHistory,
            'activePlan' => $activePlanArr,
        ];
    }

} 