<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Branch;
use App\Models\User;
use App\Models\WorkingHour;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;

class BranchService
{
    /**
     * Create a new branch.
     */
    public function createBranch(array $data): Branch
    {
        return Branch::create($data);
    }

    /**
     * Update an existing branch.
     */
    public function updateBranch(Branch $branch, array $data): Branch
    {
        $branch->update($data);

        return $branch;
    }

    public function getBranchesForUser(int $userId, array $filters = []): LengthAwarePaginator
    {
        $query = Branch::where('user_id', $userId);

        if (! empty($filters['search'])) {
            $query->where('name', 'like', "%{$filters['search']}%")
                ->orWhere('address', 'like', "%{$filters['search']}%")
                ->orWhere('phone', 'like', "%{$filters['search']}%");
        }

        if (isset($filters['status']) && $filters['status'] !== null) {
            $query->where('is_active', $filters['status'] === 'active');
        }

        $sortField     = $filters['sort']      ?? 'name';
        $sortDirection = $filters['direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        return $query->paginate(10)->withQueryString();
    }

    public function updateCurrentBranch(int $userId, int $branchId): void
    {
        $branch = Branch::where('user_id', $userId)
            ->where('id', $branchId)
            ->firstOrFail();

        $user                    = User::find($userId);
        $user->current_branch_id = $branchId;
        $user->save();
    }

    public function deleteBranch(int $userId, Branch $branch)
    {

        $branchCount = Branch::where('user_id', $userId)->count();
        if ($branchCount <= 1) {
            return redirect()
                ->route('vendor.branches.index')
                ->with('error', 'At least one branch is required. Cannot delete the last remaining branch.');
        }
        if ($branch->users()->count() > 0) {
            return redirect()
                ->route('vendor.branches.index')
                ->with('error', 'Cannot delete a branch that has users');
        }
        if ($branch->appointments()->count() > 0) {
            return redirect()
                ->route('vendor.branches.index')
                ->with('error', 'Cannot delete a branch that has appointments');
        }
        if ($branch->seats()->count() > 0) {
            return redirect()
                ->route('vendor.branches.index')
                ->with('error', 'Cannot delete a branch that has seats');
        }
        if ($branch->services()->count() > 0) {
            return redirect()
                ->route('vendor.branches.index')
                ->with('error', 'Cannot delete a branch that has services');
        }
        if (Auth::user()->current_branch_id === $branch->id) {
            $anotherBranch = Branch::where('user_id', $userId)
                ->where('id', '!=', $branch->id)
                ->first();
            if ($anotherBranch) {
                $user                    = Auth::user();
                $user->current_branch_id = $anotherBranch->id;
                $user->save();
            }
        }
        $branch->delete();

        return redirect()
            ->route('vendor.branches.index')
            ->with('success', 'Branch deleted successfully');
    }

    public function getTrashedBranchesForUser(int $userId, array $filters = []): LengthAwarePaginator
    {
        $query = Branch::onlyTrashed()->where('user_id', $userId);
        if (! empty($filters['search'])) {
            $query->where('name', 'like', "%{$filters['search']}%")
                ->orWhere('address', 'like', "%{$filters['search']}%")
                ->orWhere('phone', 'like', "%{$filters['search']}%");
        }
        $sortField     = $filters['sort']      ?? 'deleted_at';
        $sortDirection = $filters['direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        return $query->paginate(10)->withQueryString();
    }

    public function restoreBranch($id): void
    {
        $branch = Branch::onlyTrashed()->findOrFail($id);
        $branch->restore();
    }

    public function forceDeleteBranch($id)
    {
        $branch = Branch::onlyTrashed()->findOrFail($id);
        if ($branch->users()->count() > 0) {
            return redirect()
                ->route('vendor.branches.trashed')
                ->with('error', 'Cannot permanently delete a branch that has users');
        }
        if ($branch->appointments()->count() > 0) {
            return redirect()
                ->route('vendor.branches.trashed')
                ->with('error', 'Cannot permanently delete a branch that has appointments');
        }
        if ($branch->seats()->count() > 0) {
            return redirect()
                ->route('vendor.branches.trashed')
                ->with('error', 'Cannot permanently delete a branch that has seats');
        }
        if ($branch->services()->count() > 0) {
            return redirect()
                ->route('vendor.branches.trashed')
                ->with('error', 'Cannot permanently delete a branch that has services');
        }
        $branch->forceDelete();

        return redirect()
            ->route('vendor.branches.trashed')
            ->with('success', 'Branch permanently deleted');
    }

    // Centralized method to update only allow_staff
    public function updateAllowStaff(Branch $branch, bool $allowStaff): Branch
    {
        $branch->allow_staff = $allowStaff;
        $branch->save();

        return $branch;
    }

    public function getWorkingDataDetail($branchID)
    {

        $workingHour = WorkingHour::where('branch_id', $branchID)->first();

        return $workingHour ? $workingHour->working_hours : $this->getDefaultWorkingHours();

    }

    /**
     * Returns a default working hours structure for a week.
     */
    public function getDefaultWorkingHours(): array
    {
        return [
            ['day' => 'Monday', 'open' => '09:00', 'close' => '18:00', 'is_closed' => false],
            ['day' => 'Tuesday', 'open' => '09:00', 'close' => '18:00', 'is_closed' => false],
            ['day' => 'Wednesday', 'open' => '09:00', 'close' => '18:00', 'is_closed' => false],
            ['day' => 'Thursday', 'open' => '09:00', 'close' => '18:00', 'is_closed' => false],
            ['day' => 'Friday', 'open' => '09:00', 'close' => '18:00', 'is_closed' => false],
            ['day' => 'Saturday', 'open' => '09:00', 'close' => '18:00', 'is_closed' => false],
            ['day' => 'Sunday', 'open' => '09:00', 'close' => '18:00', 'is_closed' => true],
        ];
    }

    // Add more centralized branch-related methods here as needed in the future.
}
