<?php

declare(strict_types=1);

namespace App\Services;

use App\Mail\AppointmentApprovedMail;
use App\Models\Appointment;
use App\Models\Branch;
use App\Models\PlanServiceUsage;
use App\Models\PlanUsage;
use App\Models\Seat;
use App\Models\Service;
use App\Models\User;
use App\Services\Notification\NotificationService;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class VendorAppointmentService
{
    /**
     * Get paginated appointments with filters and sorting.
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getAppointments(int $branchId, array $filters = [])
    {
        $allStatus = ['pending', 'in_progress', 'completed', 'cancelled'];
        $query     = Appointment::with(['user', 'services', 'appointmentServices'])
            ->where('branch_id', $branchId)
            ->whereIn('status', $allStatus);

        // Search filter
        if (! empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($subq) use ($search) {
                    $subq->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                })
                    ->orWhere('ticket_number', 'like', "%{$search}%");
            });
        }

        // Status filter
        $status = $filters['status'] ?? 'pending';
        if ($status && $status !== 'all') {

            if ($status == 'pending') {
                $query->whereDoesntHave('appointmentServices', function ($query) {
                    $query->whereNotNull('seat_id');
                });
                $query->where('appointments.status', 'pending');

            } elseif ($status == 'approved') {
                $query->where('appointments.status', 'pending');
                $query->whereDoesntHave('appointmentServices', function ($query) {
                    $query->whereNull('seat_id');
                });
            } else {
                $query->where('appointments.status', $status);
            }

        }
        // Date filter
        if (! empty($filters['date'])) {
            $query->whereDate('appointment_date', $filters['date']);
        }

        $query->whereHas('appointmentServices');
        $query->whereHas('services');

        // Sorting
        $sortField     = $filters['sort']      ?? 'id';
        $sortDirection = $filters['direction'] ?? 'desc';
        if ($sortField === 'user.name') {
            $query->join('users', 'appointments.user_id', '=', 'users.id')
                ->orderBy('users.name', $sortDirection)
                ->select('appointments.*');
        } else {
            $query->orderBy($sortField, $sortDirection);
        }

        $appointments = $query->paginate(env('LIMIT_PAGE', 15))->withQueryString();

        return $appointments;
    }

    /**
     * Create a new appointment and attach services.
     */
    public function createAppointment(int $branchId, array $data): Appointment
    {

        $branchDetail = Branch::find($branchId);

        $ticketNumber = 'APT-'.date('Ymd').'-'.mb_strtoupper(mb_substr(uniqid(), -5));
        $appointment  = Appointment::create([
            'user_id'          => $data['user_id'],
            'branch_id'        => $branchId,
            'appointment_date' => $data['appointment_date'],
            'appointment_time' => $data['appointment_time'],
            'ticket_number'    => $ticketNumber,
            'currency_symbol'  => $branchDetail->currency_symbol,
            'currency_text'    => $branchDetail->currency_text,
            'status'           => 'pending',
            'notes'            => $data['notes'] ?? null,
            'staff_id'         => $data['staff_id'],
            'staff_json'       => $data['staff_json'],
        ]);

        $services = Service::whereIn('id', collect($data['services'])->pluck('id'))
            ->get()
            ->keyBy('id');

        foreach ($data['services'] as $serviceData) {
            $service = $services[$serviceData['id']];

            $notification_date_after_service  = null;
            $notification_after_service_count = 0;

            $serviceData = Service::where('id', $service->id)
                ->where('reminder_after_service', '!=', 0)
                ->where('total_repeat_service', '>', 0)
                ->first();

            if ($serviceData) {

                $appoinmentDate                   = $data['appointment_date'];
                $reminder_after_service           = $serviceData->reminder_after_service;
                $totalRepeatCounter               = $serviceData->total_repeat_service;
                $notification_date_after_service  = Carbon::parse($appoinmentDate)->addDays($reminder_after_service);
                $notification_after_service_count = $totalRepeatCounter;

            }

            $appointment->services()->attach($service->id, [
                'price'                            => $service->price,
                'service_name'                     => $service->name,
                'status'                           => 'pending',
                'seat_id'                          => $data['seat_id'] ?? null,
                'notification_date_after_service'  => $notification_date_after_service,
                'notification_after_service_count' => $notification_after_service_count,
                'created_at'                       => now(),
                'updated_at'                       => now(),
            ]);
        }

        $this->updateAppoinmentServiceBeforeConfirm($appointment);

        return $appointment;
    }

    /**
     * Update an existing appointment and sync services.
     */
    public function updateAppointment(Appointment $appointment, int $branchId, array $data): Appointment
    {
        $appointment->update([
            'user_id'          => $data['user_id'],
            'appointment_date' => $data['appointment_date'],
            'appointment_time' => $data['appointment_time'],
            'notes'            => $data['notes'] ?? null,
            'status'           => $data['status'],
            'staff_id'         => $data['staff_id'],
            'staff_json'       => $data['staff_json'],
        ]);

        $currentServices = $appointment->appointmentServices->keyBy('service_id');
        $services        = Service::whereIn('id', collect($data['services'])->pluck('id'))
            ->get()
            ->keyBy('id');

        $syncData = [];
        foreach ($data['services'] as $serviceData) {
            $service = $services[$serviceData['id']];

            if (isset($currentServices[$service->id])) {

                $notification_date_after_service  = null;
                $notification_after_service_count = 0;

                $serviceData = Service::where('id', $service->id)
                    ->where('reminder_after_service', '!=', 0)
                    ->where('total_repeat_service', '>', 0)
                    ->first();

                if ($serviceData) {

                    $appoinmentDate                   = $data['appointment_date'];
                    $reminder_after_service           = $serviceData->reminder_after_service;
                    $totalRepeatCounter               = $serviceData->total_repeat_service;
                    $notification_date_after_service  = Carbon::parse($appoinmentDate)->addDays($reminder_after_service);
                    $notification_after_service_count = $totalRepeatCounter;

                }

                $syncData[$service->id] = [
                    'price'                            => $currentServices[$service->id]->price,
                    'service_name'                     => $currentServices[$service->id]->service_name,
                    'seat_id'                          => $data['seat_id'] ?? null,
                    'status'                           => $data['status'],
                    'notification_date_after_service'  => $notification_date_after_service,
                    'notification_after_service_count' => $notification_after_service_count,
                    'updated_at'                       => now(),
                ];

                continue;
            }

            $syncData[$service->id] = [
                'price'        => $service->price,
                'service_name' => $service->name,
                'seat_id'      => $data['seat_id'] ?? null,
                'updated_at'   => now(),
            ];
        }
        $appointment->services()->sync($syncData);

        if ($data['status'] == 'completed') {
            $this->updatePurchasePlanRemainCount($appointment);
        }

        $this->updateAppoinmentServiceBeforeConfirm($appointment);

        if ($data['seat_id'] && $data['status'] == 'pending') {
            // notification to customer that your appoinment is apporved
            $customer = User::find($data['user_id']);
            $appointment->load('services');
            $branch = Branch::find($appointment->branch_id);
            $staff  = null;
            if ($data['staff_id']) {
                $staff = User::find($data['staff_id']);
            }
            try {
                NotificationService::sendMail(
                    $customer->email,
                    new AppointmentApprovedMail($appointment, $customer, $branch, $staff)
                );
            } catch (\Exception $e) {
                // Log or ignore
            }
        }

        return $appointment;
    }

    /**
     * Delete an appointment and detach services.
     */
    public function deleteAppointment(Appointment $appointment, int $branchId): ?bool
    {
        $appointment->where('branch_id', $branchId)->firstOrFail();
        $appointment->services()->detach();

        return $appointment->delete();
    }

    /**
     * Update the status of a specific service in an appointment.
     */
    public function updateServiceStatus(Appointment $appointment, int $serviceId, string $status): void
    {
        $appointment->updatetimeonProcess($appointment->id, $serviceId, $status);
    }

    /**
     * Get calendar data for appointments in a branch.
     */
    public function getCalendarData(int $branchId, Carbon $start, Carbon $end): Collection
    {
        $appointments = Appointment::with(['user', 'services'])
            ->where('branch_id', $branchId)
            ->where('appointment_date', '>=', now()->startOfDay())
            ->whereBetween('appointment_date', [$start, $end])
            ->get();

        return $appointments;
    }

    /**
     * Get seat map data for a branch.
     */
    public function getSeatMapData(int $branchId, ?int $staffId = null): array
    {
        if (! $branchId) {
            // TODO: Handle exception properly
        }

        $seatsQuery = Seat::with(['staff', 'appointmentServices' => function ($query) {
            $query->whereHas('appointment', function ($q) {
                $q->where('status', 'in_progress');
            })->with(['appointment.user', 'service']);
        }])->where('staff_id', '<>', '')->where('branch_id', $branchId);

        // If staffId is provided, filter seats for that staff only
        if ($staffId) {
            $seatsQuery->where('staff_id', $staffId);
        }
        $seats                  = $seatsQuery->get();
        $unassignedAppointments = Appointment::with(['user', 'services'])
            ->where('branch_id', $branchId)
            ->where('status', 'pending')
            ->whereDoesntHave('appointmentServices', function ($query) {
                $query->whereNotNull('seat_id');
            })
            ->whereHas('appointmentServices')
            ->get();

        $assignedPendingAppointmentsQuery = Appointment::with(['user', 'services', 'appointmentServices.seat'])
            ->where('branch_id', $branchId)
            ->where('status', 'pending')
            ->whereHas('appointmentServices', function ($query) {
                $query->whereNotNull('seat_id');
            });

        if ($staffId) {
            $assignedPendingAppointmentsQuery->whereHas('appointmentServices.seat', function ($query) use ($staffId) {
                $query->where('staff_id', $staffId);
            });
        }
        $assignedPendingAppointments = $assignedPendingAppointmentsQuery->get();

        return [
            'seats'                       => $seats,
            'unassignedAppointments'      => $unassignedAppointments,
            'assignedPendingAppointments' => $assignedPendingAppointments,
        ];
    }

    /**
     * Check if a seat is available for in-progress or pending appointments.
     */
    public function checkSeatAvailability(Seat $seat, int $branchId): bool
    {
        if ($seat->branch_id !== $branchId) {
            return false;
        }
        $hasInProgressAppointments = $seat->appointmentServices()
            ->whereIn('status', ['in_progress', 'pending'])
            ->whereHas('appointment', function ($query) use ($branchId) {
                $query->where('status', 'in_progress')->where('branch_id', $branchId);
            })
            ->exists();

        return ! $hasInProgressAppointments;
    }

    /**
     * Update seat status for an appointment.
     */
    public function updateSeatStatus(Appointment $appointment, int $branchId, string $status, ?int $seatId): void
    {
        $appointment->update([
            'status' => $status,
        ]);

        if ($status == 'completed' || $status == 'cancelled') {

            if ($status == 'completed') {
                $this->updatePurchasePlanRemainCount($appointment);
            }

            $appointment->appointmentServices()->update([
                'seat_id' => $seatId,
                'status'  => $status,
            ]);
        } else {
            $appointment->appointmentServices()->update([
                'seat_id' => $seatId,
            ]);
        }
    }

    /**
     * Update the purchased plan service counter upgrade after complete the service
     */
    public function updatePurchasePlanRemainCount(Appointment $appointment)
    {
        $userID          = $appointment->user_id;
        $currentServices = $appointment->appointmentServices;
        $getAllPlans     = PlanUsage::where('user_id', $userID)
            ->where('status', 'active')
            ->whereDate('expires_at', '>=', Carbon::today())
            ->pluck('id')
            ->toArray();

        // this is the collection of service that is not in purchase plan
        $remainService = [];

        foreach ($currentServices as $serviceDetails) {

            $serviceDetails->end_time = Carbon::now();

            if ($serviceDetails->notification_after_service_count > 0 && $serviceDetails->notification_date_after_service) {

                // Get the service with reminder_after_service not zero
                $service = Service::where('id', $serviceDetails->service_id)
                    ->where('reminder_after_service', '!=', 0)
                    ->where('total_repeat_service', '>', 0)
                    ->first();

                if ($service) {
                    // Add reminder_after_service days to current date
                    $serviceDetails->notification_after_service_count = ($serviceDetails->notification_after_service_count - 1);
                    if ($serviceDetails->notification_after_service_count == 0) {
                        $serviceDetails->notification_date_after_service = null;
                        $serviceDetails->notification_status             = 1;
                    } else {
                        $serviceDetails->notification_date_after_service = Carbon::now()->addDays($service->reminder_after_service);
                        $serviceDetails->notification_status             = 0;
                    }
                } else {
                    $serviceDetails->notification_after_service_count = 0;
                    $serviceDetails->notification_date_after_service  = null;
                    $serviceDetails->notification_status              = 1;

                }

            }

            $serviceDetails->save();

            $checkusageCount = PlanServiceUsage::whereIn('plan_usage_id', $getAllPlans)
                ->where('service_id', $serviceDetails->service_id)
                ->where('remaining_count', '>', 0)
                ->first();

            if (! $checkusageCount) {
                array_push($remainService, $serviceDetails->id);

                continue;
            }

            $checkusageCount->increment('used_count');
            $checkusageCount->decrement('remaining_count');
            $serviceDetails->plan_used_service = $checkusageCount->id;
            $serviceDetails->save();
        }
    }

    public function updateAppoinmentServiceBeforeConfirm(Appointment $appointment)
    {
        $userID          = $appointment->user_id;
        $currentServices = $appointment->appointmentServices;
        $getAllPlans     = PlanUsage::where('user_id', $userID)
            ->where('status', 'active')
            ->whereDate('expires_at', '>=', Carbon::today())
            ->pluck('id')
            ->toArray();

        // this is the collection of service that is not in purchase plan
        $remainService = [];

        foreach ($currentServices as $serviceDetails) {

            $serviceDetails->end_time = Carbon::now();
            $serviceDetails->save();

            $checkusageCount = PlanServiceUsage::whereIn('plan_usage_id', $getAllPlans)
                ->where('service_id', $serviceDetails->service_id)
                ->where('remaining_count', '>', 0)
                ->first();

            if (! $checkusageCount) {
                array_push($remainService, $serviceDetails->id);

                continue;
            }
            $serviceDetails->plan_used_service = $checkusageCount->id;
            $serviceDetails->save();
        }
    }
}
