<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Appointment;
use App\Models\AppointmentService;
use App\Models\Seat;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class DashboardService
{
    public function getDashboardData(int $branchId): array
    {
        $today     = Carbon::today();
        $lastWeek  = Carbon::today()->subWeek();
        $lastMonth = Carbon::today()->subMonth();

        return [
            'appointmentStats'        => $this->getAppointmentStats($branchId, $today),
            'revenueStats'            => $this->getRevenueStats($branchId, $today, $lastWeek, $lastMonth),
            'monthlyRevenue'          => $this->getMonthlyRevenue($branchId),
            'serviceAnalytics'        => $this->getServiceAnalytics($branchId),
            'customerInsights'        => $this->getCustomerInsights($branchId, $lastMonth),
            'seatStats'               => $this->getSeatStats($branchId),
            'upcomingAppointments'    => $this->getUpcomingAppointments($branchId, $today),
            'recentCompletedServices' => $this->getRecentCompletedServices($branchId),
        ];
    }

    private function getAppointmentStats(int $branchId, Carbon $today): array
    {
        $todayAppointments = Appointment::where('branch_id', $branchId)
            ->whereDate('appointment_date', $today)
            ->get();

        return [
            'over_all_apoinment' => Appointment::where('branch_id', $branchId)->count(),
            'today_total'        => $todayAppointments->count(),
            'today_pending'      => $todayAppointments->where('status', 'pending')->count(),
            'today_in_progress'  => $todayAppointments->where('status', 'in_progress')->count(),
            'today_completed'    => $todayAppointments->where('status', 'completed')->count(),
            'today_cancelled'    => $todayAppointments->where('status', 'cancelled')->count(),
        ];
    }

    private function getRevenueStats(int $branchId, Carbon $today, Carbon $lastWeek, Carbon $lastMonth): array
    {
        return [
            'today'       => $this->calculateRevenue($branchId, $today, $today),
            'week'        => $this->calculateRevenue($branchId, $lastWeek, Carbon::today()),
            'month'       => $this->calculateRevenue($branchId, $lastMonth, Carbon::today()),
            'grand_total' => $this->calculateRevenue($branchId),
        ];
    }

    private function calculateRevenue(int $branchId, ?Carbon $startDate = null, ?Carbon $endDate = null): float
    {
        $query = AppointmentService::whereHas('appointment', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        });

        if ($startDate) {
            $query->whereHas('appointment', function ($query) use ($startDate) {
                $query->whereDate('appointment_date', '>=', $startDate);
            });
        }

        if ($endDate) {
            $query->whereHas('appointment', function ($query) use ($endDate) {
                $query->whereDate('appointment_date', '<=', $endDate);
            });
        }

        return (float) $query->sum('price');
    }

    private function getMonthlyRevenue(int $branchId): array
    {
        $monthlyRevenue = [];
        $currentYear    = 2025;

        for ($i = 0; $i < 12; $i++) {
            $startDate = Carbon::create($currentYear, 1, 1)->addMonths($i);
            $endDate   = Carbon::create($currentYear, 1, 1)->addMonths($i)->endOfMonth();

            $monthlyRevenue[] = $this->calculateRevenue($branchId, $startDate, $endDate);
        }

        return $monthlyRevenue;
    }

    private function getServiceAnalytics(int $branchId): Collection
    {
        return AppointmentService::whereHas('appointment', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
            ->selectRaw('service_id, COUNT(*) as total_bookings, SUM(price) as total_revenue')
            ->with('service:id,name')
            ->groupBy('service_id')
            ->orderByDesc('total_bookings')
            ->take(5)
            ->get()
            ->map(function ($item) {
                return [
                    'service_name'   => $item->service->name,
                    'total_bookings' => (int) $item->total_bookings,
                    'total_revenue'  => (float) $item->total_revenue,
                ];
            });
    }

    private function getCustomerInsights(int $branchId, Carbon $lastMonth): array
    {
        return [
            'total_customers' => Appointment::where('branch_id', $branchId)
                ->distinct('user_id')
                ->count('user_id'),
            'new_customers' => Appointment::where('branch_id', $branchId)
                ->whereDate('created_at', '>=', $lastMonth)
                ->distinct('user_id')
                ->count('user_id'),
            'repeat_customers' => Appointment::where('branch_id', $branchId)
                ->whereDate('created_at', '<', $lastMonth)
                ->distinct('user_id')
                ->count('user_id'),
        ];
    }

    private function getSeatStats(int $branchId): array
    {
        return [
            'total'       => Seat::where('branch_id', $branchId)->count(),
            'available'   => Seat::where('branch_id', $branchId)->where('status', 'available')->count(),
            'occupied'    => Seat::where('branch_id', $branchId)->where('status', 'occupied')->count(),
            'maintenance' => Seat::where('branch_id', $branchId)->whereIn('status', ['maintenance'])->count(),
            'cleaning'    => Seat::where('branch_id', $branchId)->whereIn('status', ['cleaning'])->count(),
        ];
    }

    private function getUpcomingAppointments(int $branchId, Carbon $today): Collection
    {
        return Appointment::with(['services', 'user'])
            ->where('branch_id', $branchId)
            ->where('status', 'pending')
            ->whereDate('appointment_date', '>=', $today)
            ->whereDate('appointment_date', '<=', $today->copy()->addDays(7))
            ->orderBy('appointment_date')
            ->orderBy('appointment_time')
            ->take(5)
            ->get()
            ->map(fn ($appointment) => [
                'id'            => $appointment->id,
                'customer_name' => $appointment->user->name,
                'date'          => $appointment->appointment_date->format('M d, Y'),
                'time'          => is_string($appointment->appointment_time)
                    ? Carbon::parse($appointment->appointment_time)->format('h:i A')
                    : $appointment->appointment_time->format('h:i A'),
                'ticket_number'  => $appointment->ticket_number,
                'services_count' => $appointment->services->count(),
                'status'         => $appointment->status,
            ]);
    }

    private function getRecentCompletedServices(int $branchId): Collection
    {
        return AppointmentService::with(['appointment.user', 'service', 'seat'])
            ->whereHas('appointment', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })
            ->where('status', 'completed')
            ->orderBy('end_time', 'desc')
            ->take(5)
            ->get()
            ->map(fn ($service) => [
                'id'            => $service->id,
                'customer_name' => $service->appointment->user->name,
                'service_name'  => $service->service->name ?? 'Unknown Service',
                'seat'          => $service->seat->name    ?? 'No Seat',
                'completed_at'  => $service->end_time ? $service->end_time->diffForHumans() : $service->updated_at->diffForHumans(),
                'duration'      => $service->start_time && $service->end_time
                    ? $service->start_time->diffInMinutes($service->end_time).' min'
                    : 'Unknown',
            ]);
    }
}
