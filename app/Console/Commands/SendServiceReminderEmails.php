<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Mail\ServiceReminderMail;
use App\Models\AppointmentService;
use App\Services\Notification\NotificationService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendServiceReminderEmails extends Command
{
    protected $signature = 'app:send-service-reminders';

    protected $description = 'Send reminder emails for services that need to be booked again';

    public function handle(): void
    {
        $this->info('Starting to send service reminder emails...');

        $appointmentServices = AppointmentService::query()
            ->with(['appointment.user', 'service'])
            ->where('notification_status', 0)
            ->where('notification_after_service_count', '>', 0)
            ->whereDate('notification_date_after_service', '=', Carbon::today())
            ->get();

        $count = 0;
        foreach ($appointmentServices as $appointmentService) {
            try {
                $user = $appointmentService->appointment->user;

                if (! $user || ! $user->email) {
                    continue;
                }

                NotificationService::sendMail(
                    $user->email,
                    new ServiceReminderMail($appointmentService)
                );

                // Update notification status
                $appointmentService->update([
                    'notification_status' => 1,
                ]);

                $count++;
                $this->info("Sent reminder email for appointment service #{$appointmentService->id}");
            } catch (\Exception $e) {
                $this->error("Failed to send reminder for appointment service #{$appointmentService->id}: {$e->getMessage()}");
            }
        }

        $this->info("Completed sending {$count} reminder emails");
    }
}
