<?php

namespace App\Listeners;

use App\Mail\EmailVerificationMail;
use App\Services\Notification\NotificationService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Log;

class SendCustomEmailVerificationNotification
{
    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        $user = $event->user;

        // Only send verification email if the user hasn't verified their email yet
        if (is_null($user->email_verified_at)) {
            // Use defer for better user experience
            defer(function () use ($user) {
                try {
                    // Create the mail instance inside defer to ensure routes are available
                    $mail = new EmailVerificationMail($user);
                    NotificationService::sendMail(
                        $user->email,
                        $mail
                    );
                } catch (\Exception $e) {
                    // Log the error but don't fail the registration
                    Log::error('Failed to send verification email: ' . $e->getMessage());
                }
            });
        }
    }
}
