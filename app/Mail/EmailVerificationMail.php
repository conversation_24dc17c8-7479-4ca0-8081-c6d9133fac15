<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class EmailVerificationMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $verificationUrl;

    /**
     * Create a new message instance.
     */
    public function __construct($user)
    {
        $this->user = $user;
        // Don't generate URL in constructor - do it in build method
    }

    /**
     * Generate the email verification URL
     */
    private function generateVerificationUrl($user)
    {
        $token = base64_encode($user->email . '|' . $user->created_at->timestamp . '|' . config('app.key'));

        // Use manual URL construction to avoid route helper issues
        $baseUrl = rtrim(config('app.url'), '/');
        return $baseUrl . '/auth/verify-email/' . urlencode($token) . '?email=' . urlencode($user->email);
    }

    /**
     * Build the message.
     */
    public function build()
    {
        // Generate the verification URL when building the email
        $this->verificationUrl = $this->generateVerificationUrl($this->user);

        return $this->subject('Verify Your Email Address')
            ->view('emails.email_verification');
    }
}
