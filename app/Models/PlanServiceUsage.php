<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PlanServiceUsage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'plan_usage_id',
        'service_id',
        'service_name',
        'used_count',
        'remaining_count',
    ];

    public function planUsage()
    {
        return $this->belongsTo(PlanUsage::class);
    }

    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    public function useService($appointmentId = null)
    {
        if ($this->remaining_count > 0) {
            $this->used_count++;
            $this->remaining_count--;
            $this->appointment_id = $appointmentId;
            $this->save();

            return true;
        }

        return false;
    }
}
