<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Scopes\TenantScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class Service extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'branch_id',
        'name',
        'description',
        'gender',
        'reminder_after_service',
        'total_repeat_service',
        'duration_minutes',
        'price',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'duration_minutes'       => 'integer',
        'price'                  => 'decimal:2',
        'is_active'              => 'boolean',
        'reminder_after_service' => 'integer',
        'total_repeat_service'   => 'integer',
    ];

    /**
     * Get the branch that owns the service.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the appointments for this service.
     */
    public function appointments(): BelongsToMany
    {
        return $this->belongsToMany(Appointment::class, 'appointment_services')
            ->withPivot(['status', 'start_time', 'end_time', 'estimated_end_time', 'service_notes'])
            ->withTimestamps();
    }

    /**
     * Get the appointment services for this service.
     */
    public function appointmentServices(): HasMany
    {
        return $this->hasMany(AppointmentService::class);
    }

    protected static function booted(): void
    {
        self::addGlobalScope(new TenantScope);
    }
}
