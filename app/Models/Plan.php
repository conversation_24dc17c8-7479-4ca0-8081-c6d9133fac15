<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Scopes\TenantScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

final class Plan extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'branch_id',
        'name',
        'price',
        'validity_days',
        'description',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price'         => 'decimal:2',
        'validity_days' => 'integer',
        'status'        => 'string',
    ];

    /**
     * Get the branch that owns the plan.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the services for this plan.
     */
    public function services(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'plan_services')
            ->withPivot(['allowed_count'])
            ->withTimestamps();
    }

    /**
     * Get the plan services for this plan.
     */
    public function planServices(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'plan_services')
            ->withPivot(['allowed_count'])
            ->withTimestamps();
    }

    /**
     * Scope a query to only include active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive plans.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Scope a query to only include plans for the current branch.
     */
    public function scopeForCurrentBranch($query)
    {
        return $query->where('branch_id', Auth::user()->current_branch_id);
    }

    protected static function booted(): void
    {
        self::addGlobalScope(new TenantScope);
    }
}
