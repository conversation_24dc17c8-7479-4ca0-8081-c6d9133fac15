<?php

declare(strict_types=1);

namespace App\Models;

use App\Mail\EmailVerificationMail;
use App\Models\Scopes\TenantScope;
use App\Services\Notification\NotificationService;
use Exception;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

final class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens,HasFactory, HasRoles, Notifiable;

    protected $fillable = [
        'name',
        'company_name',
        'company_domain',
        'tenant_id',
        'branch_id',
        'current_branch_id',
        'customer_branch_ids',
        'email',
        'password',
        'phone',
        'gender',
        'address',
        'logo',
        'profile',
        'is_active',
        'parent_user_id',
        'anniversary',
        'date_of_birth',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password'          => 'hashed',
        'is_active'         => 'boolean',
        'anniversary'       => 'date',
        'date_of_birth'     => 'date',
    ];

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'current_branch_id');
    }

    public function managedBranches(): HasMany
    {
        return $this->hasMany(Branch::class, 'user_id');
    }

    public function seats(): HasMany
    {
        return $this->hasMany(Seat::class, 'staff_id');
    }

    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class);
    }

    public function isStaff(): bool
    {
        return $this->hasRole('staff');
    }

    protected static function booted(): void
    {
        self::addGlobalScope(new TenantScope);
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function vendorSubscriptions(): HasMany
    {
        return $this->hasMany(VendorSubscription::class);
    }

    public function activeSubscription(): HasMany
    {
        return $this->hasMany(VendorSubscription::class)->where('status', 'active');
    }

    public function getCurrentSubscription(): ?VendorSubscription
    {
        return $this->vendorSubscriptions()
            ->where('status', 'active')
            ->where('ends_at', '>', now())
            ->first();
    }

    public function hasActiveSubscription(): bool
    {
        return $this->getCurrentSubscription() !== null;
    }

    public function isSubscriptionExpired(): bool
    {
        $subscription = $this->getCurrentSubscription();

        return $subscription ? $subscription->isExpired() : true;
    }

    public function canPerformAction(string $action): bool
    {
        if (! $this->hasRole('vendor')) {
            return true; // Non-vendors are not limited
        }

        $subscription = $this->getCurrentSubscription();
        if (! $subscription) {
            return false; // No active subscription
        }

        return match ($action) {
            'add_service'     => $subscription->canAddService(),
            'add_appointment' => $subscription->canAddAppointment(),
            'add_seat'        => $subscription->canAddSeat(),
            'add_branch'      => $subscription->canAddBranch(),
            'add_staff'       => $subscription->canAddStaff(),
            default           => true,
        };
    }

    /**
     * The parent user (creator/owner) of this user.
     */
    public function parentUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'parent_user_id');
    }

    /**
     * Branch users created by this user.
     */
    public function branchUsers(): HasMany
    {
        return $this->hasMany(User::class, 'parent_user_id');
    }

    public function getRedirectUrl(): string
    {

        $protocol      = request()->secure() ? 'https' : 'http';
        $centralDomain = config('services.central_domain.url');

        if ($this->hasRole('admin')) {
            return $protocol.'://'.$centralDomain.'/siteadmin/dashboard';
        }

        if ($this->hasRole('vendor') && $this->company_domain) {
            return $protocol.'://'.$this->company_domain.'.'.$centralDomain.'/vendor/dashboard';
        }

        if ($this->hasRole('staff') && $this->branch_id) {
            $branch = Branch::findOrFail($this->branch_id);
            $parent = User::findOrFail($branch->user_id);

            if (! $this->tenant_id) {
                $this->tenant_id      = $parent->tenant_id;
                $this->company_name   = $parent->company_name;
                $this->parent_user_id = $parent->id;
                $this->save();
            }

            return $protocol.'://'.$parent->company_domain.'.'.$centralDomain.'/own-appoinment';
        }

        if ($this->hasRole('branchuser')) {
            $parent = User::find($this->parent_user_id);

            return $protocol.'://'.$parent->company_domain.'.'.$centralDomain.'/vendor/dashboard';
        }

        if ($this->hasRole('customer')) {
            return $protocol.'://'.$centralDomain.'/user/dashboard';
        }

        // Default: logout and redirect back
        return '';
    }

    /**
     * Send the email verification notification using our custom email system.
     */
    public function sendEmailVerificationNotification()
    {
        // Use our custom email verification system instead of Laravel's default
        defer(function () {
            try {
                NotificationService::sendMail(
                    $this->email,
                    new EmailVerificationMail($this)
                );
            } catch (Exception $e) {
                Log::error('Failed to send verification email: ' . $e->getMessage());
            }
        });
    }
}
