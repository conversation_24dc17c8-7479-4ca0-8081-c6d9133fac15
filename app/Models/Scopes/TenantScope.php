<?php

declare(strict_types=1);

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class TenantScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        // Only apply tenant scoping when we're in a tenant context
        if (app()->bound('tenant') && tenant()) {
            $tenantId = tenant('id');

            // For models that have a direct tenant relationship
            if ($model->getTable() === 'users') {
                $builder->where('tenant_id', $tenantId);
            }
            // For models that are related through user (branches, services, etc.)
            elseif (in_array($model->getTable(), ['branches'])) {
                $builder->whereHas('user', function ($query) use ($tenantId) {
                    $query->where('tenant_id', $tenantId);
                });
            }
            // For models that are related through branch
            elseif (in_array($model->getTable(), ['services', 'seats', 'appointments', 'terms_conditions'])) {
                $builder->whereHas('branch.user', function ($query) use ($tenantId) {
                    $query->where('tenant_id', $tenantId);
                });
            }
        }
    }
}
