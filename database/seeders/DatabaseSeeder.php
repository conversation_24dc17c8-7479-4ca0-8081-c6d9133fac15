<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

final class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        // Create roles
        $adminRole    = Role::create(['name' => 'admin']);
        $vendorRole   = Role::create(['name' => 'vendor']);
        $staffRole    = Role::create(['name' => 'staff']);
        $customerRole = Role::create(['name' => 'customer']);

        // Create permissions
        $permissions = [
            // Branch permissions
            'view branches', 'create branches', 'edit branches', 'delete branches',
            // Staff permissions
            'view staff', 'create staff', 'edit staff', 'delete staff',
            // Service permissions
            'view services', 'create services', 'edit services', 'delete services',
            // Seat permissions
            'view seats', 'create seats', 'edit seats', 'delete seats',
            // Appointment permissions
            'view appointments', 'create appointments', 'edit appointments', 'delete appointments',
            // Terms & Conditions permissions
            'view terms', 'create terms', 'edit terms', 'delete terms',
            // Vendor permissions
            'view vendors', 'create vendors', 'edit vendors', 'delete vendors',
            // Working hours permissions
            'view working-hours', 'edit working-hours',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Assign permissions to roles
        $adminRole->givePermissionTo($permissions);

        $vendorPermissions = [
            'view branches', 'create branches', 'edit branches', 'delete branches',
            'view staff', 'create staff', 'edit staff', 'delete staff',
            'view services', 'create services', 'edit services', 'delete services',
            'view seats', 'create seats', 'edit seats', 'delete seats',
            'view appointments', 'create appointments', 'edit appointments', 'delete appointments',
            'view terms', 'create terms', 'edit terms', 'delete terms',
            'view working-hours', 'edit working-hours',
        ];
        $vendorRole->givePermissionTo($vendorPermissions);

        $staffPermissions = [
            'view appointments', 'create appointments', 'edit appointments',
            'view seats', 'view services',
        ];
        $staffRole->givePermissionTo($staffPermissions);

        $customerPermissions = [
            'view appointments', 'create appointments',
            'view services',
        ];
        $customerRole->givePermissionTo($customerPermissions);

        // Create a user first (owner/admin)
        $admin = User::create([
            'name'              => 'Admin User',
            'email'             => '<EMAIL>',
            'phone'             => '1234567890',
            'gender'            => 'male',
            'email_verified_at' => now(),
            'password'          => Hash::make('password'),
            'remember_token'    => Str::random(10),
            'created_at'        => now(),
            'updated_at'        => now(),
        ]);

        $admin->assignRole('admin');
        $adminId = $admin->id;

        // Create branches
        $branchId = DB::table('branches')->insertGetId([
            'user_id'    => $adminId,
            'name'       => 'Main Branch',
            'address'    => '123 Main Street',
            'phone'      => '9876543210',
            'is_active'  => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Update admin user with branch info
        $admin->update([
            'branch_id'         => $branchId,
            'current_branch_id' => $branchId,
        ]);

        // Create terms and conditions
        DB::table('terms_conditions')->insert([
            'user_id'    => $adminId,
            'branch_id'  => $branchId,
            'title'      => 'Standard Terms',
            'condition'  => 'These are the standard terms and conditions for our service.',
            'is_default' => true,
            'is_active'  => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create staff members
        $staffIds  = [];
        $staffData = [
            [
                'name'   => 'John Stylist',
                'email'  => '<EMAIL>',
                'phone'  => '1111222233',
                'gender' => 'male',
            ],
            [
                'name'   => 'Jane Beautician',
                'email'  => '<EMAIL>',
                'phone'  => '4444555566',
                'gender' => 'female',
            ],
            [
                'name'   => 'Mike Barber',
                'email'  => '<EMAIL>',
                'phone'  => '7777888899',
                'gender' => 'male',
            ],
        ];

        foreach ($staffData as $staffMember) {
            $staff = User::create([
                'name'              => $staffMember['name'],
                'email'             => $staffMember['email'],
                'phone'             => $staffMember['phone'],
                'gender'            => $staffMember['gender'],
                'branch_id'         => $branchId,
                'current_branch_id' => $branchId,
                'email_verified_at' => now(),
                'password'          => Hash::make('password'),
                'remember_token'    => Str::random(10),
                'created_at'        => now(),
                'updated_at'        => now(),
            ]);

            $staff->assignRole('staff');
            $staffIds[] = $staff->id;
        }

        // Create salon seats
        $seatIds      = [];
        $seatNames    = ['Chair 1', 'Chair 2', 'Chair 3', 'Chair 4', 'VIP Seat'];
        $seatStatuses = ['available', 'available', 'occupied', 'maintenance', 'available'];

        for ($i = 0; $i < count($seatNames); $i++) {
            $seatIds[] = DB::table('seats')->insertGetId([
                'branch_id'  => $branchId,
                'staff_id'   => $i < count($staffIds) ? $staffIds[$i] : null,
                'name'       => $seatNames[$i],
                'status'     => $seatStatuses[$i],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Create services
        $serviceIds  = [];
        $serviceData = [
            [
                'name'             => 'Haircut',
                'description'      => 'Basic haircut service',
                'duration_minutes' => 30,
                'price'            => 25.00,
            ],
            [
                'name'             => 'Hair Coloring',
                'description'      => 'Full hair coloring service',
                'duration_minutes' => 90,
                'price'            => 75.00,
            ],
            [
                'name'             => 'Styling',
                'description'      => 'Hair styling for special occasions',
                'duration_minutes' => 45,
                'price'            => 40.00,
            ],
            [
                'name'             => 'Facial',
                'description'      => 'Basic facial treatment',
                'duration_minutes' => 60,
                'price'            => 50.00,
            ],
            [
                'name'             => 'Manicure',
                'description'      => 'Basic manicure service',
                'duration_minutes' => 30,
                'price'            => 20.00,
            ],
        ];

        foreach ($serviceData as $service) {
            $serviceIds[] = DB::table('services')->insertGetId([
                'branch_id'        => $branchId,
                'name'             => $service['name'],
                'description'      => $service['description'],
                'duration_minutes' => $service['duration_minutes'],
                'price'            => $service['price'],
                'is_active'        => true,
                'created_at'       => now(),
                'updated_at'       => now(),
            ]);
        }

        // Create customers
        $customerIds  = [];
        $customerData = [
            [
                'name'   => 'Customer One',
                'email'  => '<EMAIL>',
                'phone'  => '1212121212',
                'gender' => 'male',
            ],
            [
                'name'   => 'Customer Two',
                'email'  => '<EMAIL>',
                'phone'  => '2323232323',
                'gender' => 'female',
            ],
            [
                'name'   => 'Customer Three',
                'email'  => '<EMAIL>',
                'phone'  => '3434343434',
                'gender' => 'other',
            ],
        ];

        foreach ($customerData as $customerData) {
            $customer = User::create([
                'name'              => $customerData['name'],
                'email'             => $customerData['email'],
                'phone'             => $customerData['phone'],
                'gender'            => $customerData['gender'],
                'branch_id'         => $branchId,
                'current_branch_id' => $branchId,
                'email_verified_at' => now(),
                'password'          => Hash::make('password'),
                'remember_token'    => Str::random(10),
                'created_at'        => now(),
                'updated_at'        => now(),
            ]);

            $customer->assignRole('customer');
            $customerIds[] = $customer->id;
        }

        // Create appointments
        $today           = Carbon::today();
        $appointmentData = [
            [
                'user_id'       => $customerIds[0],
                'date'          => $today->format('Y-m-d'),
                'time'          => '10:00:00',
                'status'        => 'completed',
                'ticket_number' => 1001,
            ],
            [
                'user_id'       => $customerIds[1],
                'date'          => $today->format('Y-m-d'),
                'time'          => '14:00:00',
                'status'        => 'in_progress',
                'ticket_number' => 1002,
            ],
            [
                'user_id'       => $customerIds[2],
                'date'          => $today->format('Y-m-d'),
                'time'          => '16:00:00',
                'status'        => 'pending',
                'ticket_number' => 1003,
            ],
            [
                'user_id'       => $customerIds[0],
                'date'          => $today->addDays(1)->format('Y-m-d'),
                'time'          => '11:00:00',
                'status'        => 'pending',
                'ticket_number' => 1004,
            ],
            [
                'user_id'       => $customerIds[1],
                'date'          => $today->addDays(2)->format('Y-m-d'),
                'time'          => '15:30:00',
                'status'        => 'pending',
                'ticket_number' => 1005,
            ],
        ];

        $appointmentIds = [];
        foreach ($appointmentData as $appointment) {
            $appointmentIds[] = DB::table('appointments')->insertGetId([
                'user_id'          => $appointment['user_id'],
                'branch_id'        => $branchId,
                'appointment_date' => $appointment['date'],
                'appointment_time' => $appointment['time'],
                'ticket_number'    => $appointment['ticket_number'],
                'status'           => $appointment['status'],
                'notes'            => 'Sample appointment notes',
                'created_at'       => now(),
                'updated_at'       => now(),
            ]);
        }

        // Create appointment services
        $now                    = Carbon::now();
        $appointmentServiceData = [
            [
                'appointment_id' => $appointmentIds[0],
                'service_id'     => $serviceIds[0],
                'seat_id'        => $seatIds[0],
                'status'         => 'completed',
                'start_time'     => $now->copy()->subHours(3),
                'end_time'       => $now->copy()->subHours(2)->subMinutes(30),
            ],
            [
                'appointment_id' => $appointmentIds[0],
                'service_id'     => $serviceIds[2],
                'seat_id'        => $seatIds[0],
                'status'         => 'completed',
                'start_time'     => $now->copy()->subHours(2)->subMinutes(30),
                'end_time'       => $now->copy()->subHours(1)->subMinutes(45),
            ],
            [
                'appointment_id'     => $appointmentIds[1],
                'service_id'         => $serviceIds[1],
                'seat_id'            => $seatIds[2],
                'status'             => 'in_progress',
                'start_time'         => $now->copy()->subMinutes(45),
                'end_time'           => null,
                'estimated_end_time' => $now->copy()->addMinutes(45),
            ],
            [
                'appointment_id' => $appointmentIds[2],
                'service_id'     => $serviceIds[3],
                'seat_id'        => null,
                'status'         => 'pending',
                'start_time'     => null,
                'end_time'       => null,
            ],
            [
                'appointment_id' => $appointmentIds[3],
                'service_id'     => $serviceIds[0],
                'seat_id'        => null,
                'status'         => 'pending',
                'start_time'     => null,
                'end_time'       => null,
            ],
            [
                'appointment_id' => $appointmentIds[3],
                'service_id'     => $serviceIds[4],
                'seat_id'        => null,
                'status'         => 'pending',
                'start_time'     => null,
                'end_time'       => null,
            ],
            [
                'appointment_id' => $appointmentIds[4],
                'service_id'     => $serviceIds[2],
                'seat_id'        => null,
                'status'         => 'pending',
                'start_time'     => null,
                'end_time'       => null,
            ],
        ];

        foreach ($appointmentServiceData as $service) {
            DB::table('appointment_services')->insert([
                'appointment_id'     => $service['appointment_id'],
                'service_id'         => $service['service_id'],
                'seat_id'            => $service['seat_id'],
                'start_time'         => $service['start_time'],
                'end_time'           => $service['end_time'],
                'estimated_end_time' => $service['estimated_end_time'] ?? null,
                'service_notes'      => 'Sample service notes',
                'status'             => $service['status'],
                'created_at'         => now(),
                'updated_at'         => now(),
            ]);
        }

        // Create working hours for the branch
        $days             = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
        $openTime         = '09:00:00';
        $closeTime        = '18:00:00';
        $weekendCloseTime = '16:00:00';

        foreach ($days as $index => $day) {
            DB::table('working_hours')->insert([
                'branch_id'  => $branchId,
                'day'        => $day,
                'open_time'  => $openTime,
                'close_time' => in_array($day, ['sat', 'sun']) ? $weekendCloseTime : $closeTime,
                'is_closed'  => $day === 'sun', // Closed on Sundays
                'day_order'  => $index + 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
