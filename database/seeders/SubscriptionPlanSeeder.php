<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Seeder;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $plans = [
            [
                'name'                       => 'Starter',
                'description'                => 'Perfect for small salons just getting started',
                'price'                      => 2499.00, // ₹2,499/month
                'billing_cycle'              => 'monthly',
                'max_services'               => 10,
                'max_appointments_per_month' => 100,
                'max_seats'                  => 3,
                'max_branches'               => 1,
                'max_staff'                  => 2,
                'has_analytics'              => false,
                'has_api_access'             => false,
                'has_custom_branding'        => false,
                'has_priority_support'       => false,
                'is_active'                  => true,
                'sort_order'                 => 1,
            ],
            [
                'name'                       => 'Professional',
                'description'                => 'Ideal for growing salons with multiple staff',
                'price'                      => 4999.00, // ₹4,999/month
                'billing_cycle'              => 'monthly',
                'max_services'               => 25,
                'max_appointments_per_month' => 500,
                'max_seats'                  => 8,
                'max_branches'               => 2,
                'max_staff'                  => 10,
                'has_analytics'              => true,
                'has_api_access'             => false,
                'has_custom_branding'        => false,
                'has_priority_support'       => false,
                'is_active'                  => true,
                'sort_order'                 => 2,
            ],
            [
                'name'                       => 'Business',
                'description'                => 'For established salons with multiple locations',
                'price'                      => 8499.00, // ₹8,499/month
                'billing_cycle'              => 'monthly',
                'max_services'               => 50,
                'max_appointments_per_month' => 1000,
                'max_seats'                  => 15,
                'max_branches'               => 5,
                'max_staff'                  => 25,
                'has_analytics'              => true,
                'has_api_access'             => true,
                'has_custom_branding'        => true,
                'has_priority_support'       => false,
                'is_active'                  => true,
                'sort_order'                 => 3,
            ],
            [
                'name'                       => 'Enterprise',
                'description'                => 'Unlimited everything for large salon chains',
                'price'                      => 16999.00, // ₹16,999/month
                'billing_cycle'              => 'monthly',
                'max_services'               => 0, // 0 = unlimited
                'max_appointments_per_month' => 0, // 0 = unlimited
                'max_seats'                  => 0, // 0 = unlimited
                'max_branches'               => 10,
                'max_staff'                  => 0, // 0 = unlimited
                'has_analytics'              => true,
                'has_api_access'             => true,
                'has_custom_branding'        => true,
                'has_priority_support'       => true,
                'is_active'                  => true,
                'sort_order'                 => 4,
            ],
            // Yearly plans with discount
            [
                'name'                       => 'Starter (Yearly)',
                'description'                => 'Perfect for small salons - Save 20% with yearly billing',
                'price'                      => 23990.00, // ₹2,499 * 12 * 0.8 = ₹23,990/year
                'billing_cycle'              => 'yearly',
                'max_services'               => 10,
                'max_appointments_per_month' => 100,
                'max_seats'                  => 3,
                'max_branches'               => 1,
                'max_staff'                  => 2,
                'has_analytics'              => false,
                'has_api_access'             => false,
                'has_custom_branding'        => false,
                'has_priority_support'       => false,
                'is_active'                  => true,
                'sort_order'                 => 5,
            ],
            [
                'name'                       => 'Professional (Yearly)',
                'description'                => 'Ideal for growing salons - Save 20% with yearly billing',
                'price'                      => 47990.00, // ₹4,999 * 12 * 0.8 = ₹47,990/year
                'billing_cycle'              => 'yearly',
                'max_services'               => 25,
                'max_appointments_per_month' => 500,
                'max_seats'                  => 8,
                'max_branches'               => 2,
                'max_staff'                  => 10,
                'has_analytics'              => true,
                'has_api_access'             => false,
                'has_custom_branding'        => false,
                'has_priority_support'       => false,
                'is_active'                  => true,
                'sort_order'                 => 6,
            ],
        ];

        foreach ($plans as $plan) {
            SubscriptionPlan::create($plan);
        }
    }
}
