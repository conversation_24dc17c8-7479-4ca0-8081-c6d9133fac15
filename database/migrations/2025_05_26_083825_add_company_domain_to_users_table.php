<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('company_name')->nullable()->after('name');
            $table->string('company_domain')->nullable()->unique()->after('company_name');
            $table->string('tenant_id')->nullable()->after('company_domain');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // For SQLite, we need to handle this differently due to limitations
        if (DB::getDriverName() === 'sqlite') {
            // Disable foreign key checks
            DB::statement('PRAGMA foreign_keys=OFF');

            // SQLite doesn't support dropping columns with constraints easily
            // We'll recreate the table without these columns
            Schema::dropIfExists('users_backup');

            // Create backup table with original structure
            Schema::create('users_backup', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('email')->unique();
                $table->timestamp('email_verified_at')->nullable();
                $table->string('password');
                $table->string('phone')->nullable();
                $table->enum('gender', ['male', 'female', 'other'])->nullable();
                $table->text('address')->nullable();
                $table->unsignedBigInteger('branch_id')->nullable();
                $table->unsignedBigInteger('current_branch_id')->nullable();
                $table->boolean('is_active')->default(true);
                $table->rememberToken();
                $table->timestamps();
            });

            // Copy data excluding the columns we want to drop
            DB::statement('INSERT INTO users_backup (id, name, email, email_verified_at, password, phone, gender, address, branch_id, current_branch_id, is_active, remember_token, created_at, updated_at) SELECT id, name, email, email_verified_at, password, phone, gender, address, branch_id, current_branch_id, is_active, remember_token, created_at, updated_at FROM users');

            // Drop original table and rename backup
            Schema::drop('users');
            Schema::rename('users_backup', 'users');

            // Re-enable foreign key checks
            DB::statement('PRAGMA foreign_keys=ON');
        } else {
            // For other databases, normal column dropping should work
            Schema::table('users', function (Blueprint $table) {
                $table->dropUnique(['company_domain']);
                $table->dropColumn(['company_name', 'company_domain', 'tenant_id']);
            });
        }
    }
};
