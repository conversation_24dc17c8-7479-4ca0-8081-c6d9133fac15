<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>HK</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#F0371D" offset="0%"></stop>
            <stop stop-color="#DE290F" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="HK">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="15"></rect>
            <path d="M12,7.19038043 C11.2020264,6.69038043 11,7.5988818 11,7.19038043 C11,6.36195331 11.8954305,5.69038043 13,5.69038043 C14.1045695,5.69038043 15,6.36195331 15,7.19038043 C14.0511475,7.19038043 13.9563611,7.69038043 13.5,7.69038043 C12.940099,7.69038043 12.7979736,7.69038043 12,7.19038043 Z M13.25,7 C13.3880712,7 13.5,6.88807119 13.5,6.75 C13.5,6.61192881 13.3880712,6.5 13.25,6.5 C13.1119288,6.5 13,6.61192881 13,6.75 C13,6.88807119 13.1119288,7 13.25,7 Z M11.4390537,8.96245671 C11.6679946,8.04903019 10.7415289,8.13763406 11.1300368,8.01140019 C11.9179178,7.75540213 12.8333248,8.39947971 13.1746555,9.44998773 C13.5159863,10.5004958 13.1539857,11.5596282 12.3661047,11.8156263 C12.0728932,10.9132139 11.5680743,10.9775752 11.427051,10.54355 C11.2540321,10.0110525 11.2101129,9.87588323 11.4390537,8.96245671 Z M12.0063875,10.0924466 C12.0490539,10.2237601 12.1900924,10.2956228 12.3214059,10.2529564 C12.4527194,10.2102901 12.5245821,10.0692516 12.4819158,9.93793807 C12.4392495,9.80662456 12.2982109,9.73476184 12.1668974,9.77742819 C12.0355839,9.82009453 11.9637212,9.96113306 12.0063875,10.0924466 Z M9.58036713,8.97656681 C10.519834,8.91203817 10.149273,8.058297 10.3893841,8.38878155 C10.8763214,9.05899318 10.5466435,10.1286279 9.65302802,10.7778775 C8.75941252,11.4271272 7.6402534,11.4101342 7.15331615,10.7399226 C7.92095398,10.182201 7.70374511,9.72197853 8.07294902,9.45373619 C8.52591844,9.12463464 8.64090028,9.04109544 9.58036713,8.97656681 Z M8.6809987,9.86531937 C8.56929676,9.94647558 8.54453456,10.102818 8.62569077,10.2145199 C8.70684697,10.3262219 8.86318939,10.3509841 8.97489133,10.2698279 C9.08659326,10.1886717 9.11135547,10.0323292 9.03019926,9.92062731 C8.94904305,9.80892537 8.79270064,9.78416317 8.6809987,9.86531937 Z M8.99258188,7.21321105 C9.34426349,8.08675668 10.04171,7.47051175 9.80159888,7.8009963 C9.31466163,8.47120792 8.19550251,8.48820095 7.30188701,7.83895129 C6.40827151,7.18970163 6.07859365,6.12006691 6.5655309,5.44985529 C7.33316873,6.00757682 7.70374511,5.65878233 8.07294902,5.92702467 C8.52591844,6.25612622 8.64090028,6.33966542 8.99258188,7.21321105 Z M7.86940783,6.63250049 C7.75770589,6.55134428 7.60136348,6.57610648 7.52020727,6.68780842 C7.43905106,6.79951036 7.46381327,6.95585278 7.5755152,7.03700898 C7.68721714,7.11816519 7.84355956,7.09340299 7.92471577,6.98170105 C8.00587197,6.86999911 7.98110977,6.71365669 7.86940783,6.63250049 Z M10.4879972,6.10928716 C9.76588156,6.71369668 10.5674881,7.18657754 10.1789802,7.06034367 C9.39109922,6.80434561 9.02909871,5.74521317 9.37042945,4.69470515 C9.7117602,3.64419713 10.6271672,3.00011955 11.4150482,3.25611761 C11.1218367,4.15853 11.5680743,4.40318566 11.427051,4.83721088 C11.2540321,5.36970838 11.2101129,5.50487763 10.4879972,6.10928716 Z M10.6932059,4.86163572 C10.7358723,4.73032222 10.6640096,4.58928369 10.532696,4.54661735 C10.4013825,4.503951 10.260344,4.57581372 10.2176777,4.70712723 C10.1750113,4.83844073 10.246874,4.97947926 10.3781876,5.02214561 C10.5095011,5.06481195 10.6505396,4.99294923 10.6932059,4.86163572 Z" id="Oval-145" fill="url(#linearGradient-1)"></path>
        </g>
    </g>
</svg>