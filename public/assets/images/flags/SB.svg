<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>SB</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#1DBE4F" offset="0%"></stop>
            <stop stop-color="#159B3F" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#0660D4" offset="0%"></stop>
            <stop stop-color="#0051BB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#2C7442" offset="0%"></stop>
            <stop stop-color="#225B34" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#FFD646" offset="0%"></stop>
            <stop stop-color="#FED02F" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="SB">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <path d="M5.92000008,5.81234217 C5.92000008,5.6729774 5.99812508,5.48187494 6.10051155,5.37948847 L6.2394886,5.24051142 C6.33918234,5.14081768 6.34187508,4.98187494 6.2394886,4.87948847 L6.10051155,4.74051142 C6.00081782,4.64081768 5.92000008,4.45298243 5.92000008,4.30765772 L5.92000008,3.81234217 C5.92000008,3.6729774 6.02701759,3.55999994 6.1723423,3.55999994 L6.66765785,3.55999994 C6.80702261,3.55999994 6.92000008,3.66701746 6.92000008,3.81234217 L6.92000008,4.30765772 C6.92000008,4.44702248 6.96614265,4.46771479 7.03596687,4.32806635 L7.30403328,3.79193354 C7.36807997,3.66384015 7.53770256,3.55999994 7.66769457,3.55999994 L8.67230558,3.55999994 C8.80910347,3.55999994 8.92000008,3.67596674 8.92000008,3.80999994 L8.92000008,3.80999994 C8.92000008,3.94807113 8.81298256,4.05999994 8.66765785,4.05999994 L8.1723423,4.05999994 C8.03297754,4.05999994 7.92000008,4.17596674 7.92000008,4.30999994 L7.92000008,4.30999994 C7.92000008,4.44807113 8.02701759,4.55999994 8.1723423,4.55999994 L8.66765785,4.55999994 C8.80702261,4.55999994 8.92000008,4.66752863 8.92000008,4.8052392 L8.92000008,6.31476068 C8.92000008,6.45020259 8.81298256,6.55999994 8.66765785,6.55999994 L8.1723423,6.55999994 C8.03297754,6.55999994 7.92000008,6.45298243 7.92000008,6.30765772 L7.92000008,5.81234217 C7.92000008,5.6729774 8.03596687,5.55999994 8.17000008,5.55999994 L8.17000008,5.55999994 C8.30807126,5.55999994 8.42000008,5.44403315 8.42000008,5.30999994 L8.42000008,5.30999994 C8.42000008,5.17192876 8.31298256,5.05999994 8.16765785,5.05999994 L7.6723423,5.05999994 C7.53297754,5.05999994 7.42000008,5.17770243 7.42000008,5.30769444 L7.42000008,6.31230545 C7.42000008,6.44910334 7.30403328,6.55999994 7.17000008,6.55999994 L7.17000008,6.55999994 C7.03192889,6.55999994 6.92000008,6.45298243 6.92000008,6.30765772 L6.92000008,5.81234217 C6.92000008,5.6729774 6.80403328,5.55999994 6.67000008,5.55999994 L6.67000008,5.55999994 C6.53192889,5.55999994 6.42000008,5.66701746 6.42000008,5.81234217 L6.42000008,6.30765772 C6.42000008,6.44702248 6.30403328,6.55999994 6.17000008,6.55999994 L6.17000008,6.55999994 C6.03192889,6.55999994 5.92000008,6.45298243 5.92000008,6.30765772 L5.92000008,5.81234217 Z" id="Rectangle-456" fill="url(#linearGradient-1)"></path>
            <rect id="Mask-Copy" fill="url(#linearGradient-2)" x="0.000237781813" y="-0.00266538434" width="21" height="15"></rect>
            <polygon id="Rectangle-253" fill="url(#linearGradient-3)" points="0.000237781813 -0.00266538434 21.0002378 -0.00266538434 0.000237781813 14.9973346"></polygon>
            <polygon id="Rectangle-253" fill="url(#linearGradient-4)" transform="translate(10.500238, 7.497335) scale(-1, 1) translate(-10.500238, -7.497335) " points="0.000237781813 -0.00266538434 21.0002378 14.9973346 0.000237781813 14.9973346"></polygon>
            <rect id="Rectangle-2" fill="url(#linearGradient-5)" x="-3.99976222" y="6.49733462" width="29" height="2"></rect>
            <path d="M2.25023778,2.87233462 L1.51550622,3.25860586 L1.65582746,2.44047024 L1.06141714,1.86106337 L1.882872,1.74169899 L2.25023778,0.997334616 L2.61760356,1.74169899 L3.43905843,1.86106337 L2.8446481,2.44047024 L2.98496935,3.25860586 L2.25023778,2.87233462 Z M7.25023778,2.87233462 L6.51550622,3.25860586 L6.65582746,2.44047024 L6.06141714,1.86106337 L6.882872,1.74169899 L7.25023778,0.997334616 L7.61760356,1.74169899 L8.43905843,1.86106337 L7.8446481,2.44047024 L7.98496935,3.25860586 L7.25023778,2.87233462 Z M7.25023778,5.87233462 L6.51550622,6.25860586 L6.65582746,5.44047024 L6.06141714,4.86106337 L6.882872,4.74169899 L7.25023778,3.99733462 L7.61760356,4.74169899 L8.43905843,4.86106337 L7.8446481,5.44047024 L7.98496935,6.25860586 L7.25023778,5.87233462 Z M2.25023778,5.87233462 L1.51550622,6.25860586 L1.65582746,5.44047024 L1.06141714,4.86106337 L1.882872,4.74169899 L2.25023778,3.99733462 L2.61760356,4.74169899 L3.43905843,4.86106337 L2.8446481,5.44047024 L2.98496935,6.25860586 L2.25023778,5.87233462 Z M4.75023778,4.37233462 L4.01550622,4.75860586 L4.15582746,3.94047024 L3.56141714,3.36106337 L4.382872,3.24169899 L4.75023778,2.49733462 L5.11760356,3.24169899 L5.93905843,3.36106337 L5.3446481,3.94047024 L5.48496935,4.75860586 L4.75023778,4.37233462 Z" id="Star-2" fill="#FFFFFF"></path>
        </g>
    </g>
</svg>