<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>FK</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#07319C" offset="0%"></stop>
            <stop stop-color="#00247E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#1F8BDE" offset="0%"></stop>
            <stop stop-color="#1075C2" offset="100%"></stop>
        </linearGradient>
        <path d="M0,3.5 L1.54164674e-17,0.509643555 C6.90218757e-18,0.228175192 0.21484375,0 0.497698784,0 L4.50230122,0 C4.77717266,0 5,0.226606369 5,0.509643555 L5,3.5 C5,6 2.5,7 2.5,7 C2.5,7 0,6 0,3.5 Z" id="path-4"></path>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#187536" offset="0%"></stop>
            <stop stop-color="#0E5023" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-8">
            <stop stop-color="#DB1E36" offset="0%"></stop>
            <stop stop-color="#D51931" offset="100%"></stop>
        </linearGradient>
        <path d="M3.5,3 L0,0 L0.5,0 L4,2.5 L5,2.5 L9,0 L9,0.25 C9,0.388071187 8.91348267,0.561798096 8.79154062,0.648899555 L5.5,3 L5.5,4 L8.8118248,6.83870697 C8.91575109,6.92778665 8.8840332,7 8.75,7 L8.75,7 C8.61192881,7 8.41348267,6.9382019 8.29154062,6.85110044 L5,4.5 L4,4.5 L0,7 L0,6.5 L3.5,4 L3.5,3 Z" id="path-9"></path>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="FK">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Mask-Copy" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="15"></rect>
            <g id="Rectangle-1105" transform="translate(13.000000, 4.000000)">
                <mask id="mask-5" fill="white">
                    <use xlink:href="#path-4"></use>
                </mask>
                <use id="Mask" fill="url(#linearGradient-3)" xlink:href="#path-4"></use>
                <path d="M0,3.5 L1.54164674e-17,0.509643555 C6.90218757e-18,0.228175192 0.21484375,0 0.497698784,0 L4.50230122,0 C4.77717266,0 5,0.226606369 5,0.509643555 L5,3.5 C5,6 2.5,7 2.5,7 C2.5,7 0,6 0,3.5 Z M2.64411871,6.36967641 C2.90723863,6.22057512 3.17088317,6.03602394 3.41551763,5.81585293 C4.09873109,5.20096081 4.5,4.43854989 4.5,3.5 L4.5,0.509643555 C4.5,0.500903502 0.49859763,0.5 0.49859763,0.5 C0.499242024,0.500576791 0.5,3.5 0.5,3.5 C0.5,4.43854989 0.901268907,5.20096081 1.58448237,5.81585293 C1.82911683,6.03602394 2.09276137,6.22057512 2.35588129,6.36967641 C2.40863204,6.3995685 2.45693356,6.42565326 2.5,6.44797816 C2.54306644,6.42565326 2.59136796,6.3995685 2.64411871,6.36967641 Z" id="Mask" fill="url(#linearGradient-6)" fill-rule="nonzero" mask="url(#mask-5)"></path>
                <path d="M1.15820313,3.47460938 C1.07082995,3.21248986 1.2157526,3 1.49538898,3 L3.50461102,3 C3.7782068,3 3.93047714,3.20856857 3.84179687,3.47460938 L3.65820312,4.02539062 C3.57082995,4.28751014 3.36282608,4.33072281 3.13365793,4.15820312 C3.13365793,4.15820312 3.22192383,4 2.5,4 C1.77807617,4 1.86634207,4.15820312 1.86634207,4.15820312 C1.66401693,4.34697233 1.43047714,4.29143143 1.34179688,4.02539062 L1.15820313,3.47460938 Z" id="Rectangle-1243" fill="url(#linearGradient-7)" mask="url(#mask-5)"></path>
                <path d="M1.5,3 C1.77068398,3.05379707 2.09047303,2.5 2.5,2.5 C2.93673774,2.5 3.22586772,3.06717997 3.5,3 C3.74585626,2.93974947 4,2.26112626 4,2 C4,1.44771525 3.32842712,1 2.5,1 C1.67157288,1 1,1.44771525 1,2 C1,2.27926677 1.22312064,2.94497163 1.5,3 Z" id="Oval-223" fill="url(#linearGradient-1)" mask="url(#mask-5)"></path>
                <path d="M-0.323223305,6.1767767 C-0.245098305,6.0986517 -0.245098305,6.0986517 0.0265281678,5.82702522 L0.327025223,5.52652817 C0.422700236,5.43085315 0.577248001,5.43080139 0.672974777,5.52652817 L0.973471832,5.82702522 C1.26435599,6.11790938 1.73548621,6.11806718 2.02652817,5.82702522 L2.32702522,5.52652817 C2.42270024,5.43085315 2.577248,5.43080139 2.67297478,5.52652817 L2.97347183,5.82702522 C3.26435599,6.11790938 3.73548621,6.11806718 4.02652817,5.82702522 L4.32702522,5.52652817 C4.42270024,5.43085315 4.577248,5.43080139 4.67297478,5.52652817 L4.97347183,5.82702522 L5.15024853,6.00380192 L5.50380192,5.65024853 L5.32702522,5.47347183 L5.02652817,5.17297478 C4.73548621,4.88193282 4.26435599,4.88209062 3.97347183,5.17297478 L3.67297478,5.47347183 C3.577248,5.56919861 3.42270024,5.56914685 3.32702522,5.47347183 L3.02652817,5.17297478 C2.73548621,4.88193282 2.26435599,4.88209062 1.97347183,5.17297478 L1.67297478,5.47347183 C1.577248,5.56919861 1.42270024,5.56914685 1.32702522,5.47347183 L1.02652817,5.17297478 C0.735486208,4.88193282 0.264355994,4.88209062 -0.0265281678,5.17297478 L-0.327025223,5.47347183 L-0.676776695,5.8232233 L-0.853553391,6 L-0.5,6.35355339 L-0.323223305,6.1767767 L-0.323223305,6.1767767 Z" id="Line-Copy" fill="url(#linearGradient-1)" fill-rule="nonzero" mask="url(#mask-5)"></path>
                <path d="M-0.323223305,4.6767767 C-0.245098305,4.5986517 -0.245098305,4.5986517 0.0265281678,4.32702522 L0.327025223,4.02652817 C0.422700236,3.93085315 0.577248001,3.93080139 0.672974777,4.02652817 L0.973471832,4.32702522 C1.26435599,4.61790938 1.73548621,4.61806718 2.02652817,4.32702522 L2.32702522,4.02652817 C2.42270024,3.93085315 2.577248,3.93080139 2.67297478,4.02652817 L2.97347183,4.32702522 C3.26435599,4.61790938 3.73548621,4.61806718 4.02652817,4.32702522 L4.32702522,4.02652817 C4.42270024,3.93085315 4.577248,3.93080139 4.67297478,4.02652817 L4.97347183,4.32702522 L5.15024853,4.50380192 L5.50380192,4.15024853 L5.32702522,3.97347183 L5.02652817,3.67297478 C4.73548621,3.38193282 4.26435599,3.38209062 3.97347183,3.67297478 L3.67297478,3.97347183 C3.577248,4.06919861 3.42270024,4.06914685 3.32702522,3.97347183 L3.02652817,3.67297478 C2.73548621,3.38193282 2.26435599,3.38209062 1.97347183,3.67297478 L1.67297478,3.97347183 C1.577248,4.06919861 1.42270024,4.06914685 1.32702522,3.97347183 L1.02652817,3.67297478 C0.735486208,3.38193282 0.264355994,3.38209062 -0.0265281678,3.67297478 L-0.327025223,3.97347183 L-0.676776695,4.3232233 L-0.853553391,4.5 L-0.5,4.85355339 L-0.323223305,4.6767767 L-0.323223305,4.6767767 Z" id="Line-Copy-3" fill="url(#linearGradient-1)" fill-rule="nonzero" mask="url(#mask-5)"></path>
                <path d="M-0.323223305,7.6767767 C-0.245098305,7.5986517 -0.245098305,7.5986517 0.0265281678,7.32702522 L0.327025223,7.02652817 C0.422700236,6.93085315 0.577248001,6.93080139 0.672974777,7.02652817 L0.973471832,7.32702522 C1.26435599,7.61790938 1.73548621,7.61806718 2.02652817,7.32702522 L2.32702522,7.02652817 C2.42270024,6.93085315 2.577248,6.93080139 2.67297478,7.02652817 L2.97347183,7.32702522 C3.26435599,7.61790938 3.73548621,7.61806718 4.02652817,7.32702522 L4.32702522,7.02652817 C4.42270024,6.93085315 4.577248,6.93080139 4.67297478,7.02652817 L4.97347183,7.32702522 L5.15024853,7.50380192 L5.50380192,7.15024853 L5.32702522,6.97347183 L5.02652817,6.67297478 C4.73548621,6.38193282 4.26435599,6.38209062 3.97347183,6.67297478 L3.67297478,6.97347183 C3.577248,7.06919861 3.42270024,7.06914685 3.32702522,6.97347183 L3.02652817,6.67297478 C2.73548621,6.38193282 2.26435599,6.38209062 1.97347183,6.67297478 L1.67297478,6.97347183 C1.577248,7.06919861 1.42270024,7.06914685 1.32702522,6.97347183 L1.02652817,6.67297478 C0.735486208,6.38193282 0.264355994,6.38209062 -0.0265281678,6.67297478 L-0.327025223,6.97347183 L-0.676776695,7.3232233 L-0.853553391,7.5 L-0.5,7.85355339 L-0.323223305,7.6767767 L-0.323223305,7.6767767 Z" id="Line-Copy-2" fill="url(#linearGradient-1)" fill-rule="nonzero" mask="url(#mask-5)"></path>
            </g>
            <g id="Rectangle-36">
                <use fill="url(#linearGradient-8)" fill-rule="evenodd" xlink:href="#path-9"></use>
                <path stroke="#FFFFFF" stroke-width="0.5" d="M3.25,3.11498373 L-0.675814352,-0.25 L0.5,-0.25 L0.645309548,-0.203433368 L4.08011626,2.25 L4.92830094,2.25 L9.25,-0.45106191 L9.25,0.25 C9.25,0.471733776 9.12210758,0.720006202 8.93685017,0.852332923 L5.75,3.12865447 L5.75,3.88501627 L8.97452264,6.64889282 C9.25702778,6.89104008 9.11322839,7.25 8.75,7.25 C8.55691391,7.25 8.30820236,7.17022759 8.14623107,7.05453381 L4.91988374,4.75 L4.07169906,4.75 L-0.25,7.45106191 L-0.25,6.37134553 L3.25,3.87134553 L3.25,3.11498373 Z"></path>
            </g>
            <path d="M0,2.5 L0,4.5 L3.5,4.5 L3.5,7.00461102 C3.5,7.2782068 3.71403503,7.5 4.00468445,7.5 L4.99531555,7.5 C5.27404508,7.5 5.5,7.2842474 5.5,7.00461102 L5.5,4.5 L9.00952148,4.5 C9.28040529,4.5 9.5,4.28596497 9.5,3.99531555 L9.5,3.00468445 C9.5,2.72595492 9.28494263,2.5 9.00952148,2.5 L5.5,2.5 L5.5,0 L3.5,0 L3.5,2.5 L0,2.5 Z" id="Rectangle-2" fill="url(#linearGradient-1)"></path>
            <polygon id="Rectangle-36" fill="url(#linearGradient-8)" points="0 3 4 3 4 2.5 4 0 5 0 5 2.5 5 3 9 3 9 4 5 4 5 4.5 5 7 4 7 4 4.5 4 4 0 4"></polygon>
        </g>
    </g>
</svg>