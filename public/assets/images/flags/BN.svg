<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>BN</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#F7E250" offset="0%"></stop>
            <stop stop-color="#F7DF38" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#262626" offset="0%"></stop>
            <stop stop-color="#0D0D0D" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="BN">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Mask-Copy" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Rectangle-2-Copy-4" fill="url(#linearGradient-1)" transform="translate(11.013030, 6.090461) rotate(20.000000) translate(-11.013030, -6.090461) " x="-3.48696979" y="4.59046107" width="29" height="3"></rect>
            <rect id="Rectangle-2-Copy-4" fill="url(#linearGradient-3)" transform="translate(9.986970, 8.909539) rotate(20.000000) translate(-9.986970, -8.909539) " x="-4.51303021" y="7.40953893" width="29" height="3"></rect>
        </g>
    </g>
</svg>