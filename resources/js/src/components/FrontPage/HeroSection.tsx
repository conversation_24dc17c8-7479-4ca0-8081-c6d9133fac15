import React from 'react';
import { FaCalendarAlt, FaMapMarkerAlt, FaPhone, FaEnvelope, FaArrowRight } from 'react-icons/fa';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';

interface Service {
    id: number;
    name: string;
    description: string;
    duration_minutes: number;
    price: number;
    image: string;
    gender?: 'male' | 'female';
}

interface CurrentBranch {
    id: number;
    name: string;
    address: string;
    email: string;
    phone: string;
    social_links: {
        icon: string;
        url: string;
    }[];
}

interface Tenant {
    id: string;
    name: string;
    address: string;
    phone: string;
    email: string;
    logo: string;
}

interface BranchMedia {
    id: number;
    image: string;
    type: string;
    status: boolean;
}

interface HeroSectionProps {
    tenant: Tenant;
    current_branch_detail: CurrentBranch;
    selectedServices: Service[];
    sliderImages: BranchMedia[];
    setIsBookingModalOpen: (open: boolean) => void;
    servicesRef: React.RefObject<HTMLDivElement>;
    SOCIAL_ICONS: Array<{
        label: string;
        value: string;
        Icon: React.ComponentType<any>;
    }>;
}

const HeroSection: React.FC<HeroSectionProps> = ({
    tenant,
    current_branch_detail,
    selectedServices,
    sliderImages,
    setIsBookingModalOpen,
    servicesRef,
    SOCIAL_ICONS
}) => {
    return (
        <div className="relative min-h-[90vh]">
            {/* Background */}
            {sliderImages.length > 0 ? (
                <div className="absolute inset-0">
                    <div className="z-10 absolute inset-0 bg-black/70"></div>
                    <Swiper
                        modules={[Navigation, Pagination, Autoplay]}
                        pagination={{ clickable: true }}
                        autoplay={{ delay: 3500 }}
                        className="h-full"
                    >
                        {sliderImages.map((img) => (
                            <SwiperSlide key={img.id}>
                                <img src={`/${img.image}`} alt="Slider" className="w-full h-full object-cover" />
                            </SwiperSlide>
                        ))}
                    </Swiper>
                </div>
            ) : (
                <div className="absolute inset-0 overflow-hidden">
                    <div className="absolute inset-0 bg-gray-50"></div>
                    {/* Subtle animated background elements */}
                    <div className="top-20 left-10 absolute bg-orange-800/5 rounded-full w-32 h-32 animate-float-slow"></div>
                    <div className="top-40 right-20 absolute bg-gray-400/5 rounded-full w-24 h-24 animate-float-medium"></div>
                    <div className="bottom-40 left-20 absolute bg-orange-800/5 rounded-full w-40 h-40 animate-float-slow"></div>
                    <div className="right-10 bottom-20 absolute bg-gray-400/5 rounded-full w-28 h-28 animate-float-fast"></div>
                </div>
            )}

            {/* Hero Content */}
            <div className="relative z-20 flex items-center py-12 min-h-[90vh]">
                <div className="mx-auto px-4 container">
                    <div className="mx-auto max-w-5xl">
                        <div className={`text-center space-y-8 ${sliderImages.length > 0 ? 'text-white' : 'text-gray-800'}`}>
                            {/* Logo & Brand */}
                            <div className="flex flex-col items-center gap-6 sm:gap-8">
                                {tenant.logo && (
                                    <div className="group relative">
                                        <img
                                            src={tenant.logo}
                                            alt={tenant.name}
                                            className="relative p-2 border-4 border-white/30 w-24 sm:w-32 transform transition-all duration-500 group-hover:scale-105"
                                        />
                                    </div>
                                )}
                                <div className='space-y-4'>
                                    <h1 className="px-2 font-black text-4xl sm:text-5xl lg:text-7xl"
                                        style={{
                                            textShadow: sliderImages.length > 0 ? '0 2px 4px rgba(0,0,0,0.3)' : 'none',
                                        }}
                                    >
                                        {current_branch_detail?.name}
                                    </h1>
                                    <p className="opacity-90 text-xl sm:text-2xl">{tenant.name}</p>
                                </div>
                            </div>

                            {/* Info Cards */}
                            <div className="flex md:flex-row flex-col justify-center gap-4 sm:gap-6 max-sm:px-4">
                                <div className={`p-4 sm:p-6 rounded-2xl backdrop-blur-sm w-full md:w-4/12 ${sliderImages.length > 0
                                    ? 'bg-white/10 hover:bg-white/20'
                                    : 'bg-white/80 hover:bg-white/90'
                                    } border border-white/20 transition-all duration-300 group`}>
                                    <div className="space-y-2 sm:space-y-4 text-center">
                                        <FaMapMarkerAlt className="mx-auto text-orange-800 size-8" />
                                        <p className="font-medium text-base sm:text-lg break-words">{current_branch_detail?.address}</p>
                                    </div>
                                </div>

                                <div className={`p-4 sm:p-6 rounded-2xl backdrop-blur-sm w-full md:w-4/12 ${sliderImages.length > 0
                                    ? 'bg-white/10 hover:bg-white/20'
                                    : 'bg-white/80 hover:bg-white/90'
                                    } border border-white/20 transition-all duration-300 group`}>
                                    <div className="space-y-2 sm:space-y-4 text-center">
                                        <FaPhone className="mx-auto text-orange-800 size-8" />
                                        <p className="font-medium text-base sm:text-lg break-words">{current_branch_detail?.phone}</p>
                                    </div>
                                </div>
                                {current_branch_detail?.email && (
                                    <div className={`p-4 sm:p-6 rounded-2xl backdrop-blur-sm w-full md:w-4/12 ${sliderImages.length > 0
                                        ? 'bg-white/10 hover:bg-white/20'
                                        : 'bg-white/80 hover:bg-white/90'
                                        } border border-white/20 transition-all duration-300 group`}>
                                        <div className="space-y-2 sm:space-y-4 text-center">
                                            <FaEnvelope className="mx-auto text-orange-800 size-8" />
                                            <p className="font-medium sm:text-lg break-all">{current_branch_detail?.email}</p>
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* CTA Section */}
                            <div className="flex flex-col items-center gap-6 px-4 sm:px-0">
                                {selectedServices.length > 0 && (
                                    <button
                                        onClick={() => {
                                            if (selectedServices.length === 0 && servicesRef.current) {
                                                servicesRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                            } else {
                                                setIsBookingModalOpen(true);
                                            }
                                        }}
                                        className="group relative bg-orange-800 hover:bg-orange-900 hover:shadow-2xl px-6 sm:px-8 py-4 rounded-2xl w-full sm:w-auto font-bold text-lg text-white transform transition-all duration-500 overflow-hidden hover:scale-105"
                                    >
                                        <span className="relative flex justify-center items-center gap-3">
                                            <FaCalendarAlt className="w-5 h-5" />
                                            Book Appointment
                                            <FaArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-2 duration-300" />
                                        </span>
                                    </button>
                                )}
                                {/* Social Links */}
                                {current_branch_detail?.social_links && current_branch_detail.social_links.length > 0 && (
                                    <div className="flex justify-center items-center gap-4">
                                        {current_branch_detail.social_links.map((link, idx) => {
                                            const Icon = SOCIAL_ICONS.find(i => i.value === link.icon)?.Icon;
                                            return (
                                                <a
                                                    key={idx}
                                                    href={link.url}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className={`p-3 sm:p-4 rounded-xl sm:rounded-2xl backdrop-blur-sm ${sliderImages.length > 0
                                                        ? 'bg-white/10 hover:bg-white/20'
                                                        : 'bg-white/80 hover:bg-white/90'
                                                        } border border-white/20 transition-all duration-300 transform hover:scale-110`}
                                                    title={SOCIAL_ICONS.find(i => i.value === link.icon)?.label || link.icon}
                                                >
                                                    {Icon && <Icon className="w-5 sm:w-6 h-5 sm:h-6 text-gray-700" />}
                                                </a>
                                            );
                                        })}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default HeroSection;
