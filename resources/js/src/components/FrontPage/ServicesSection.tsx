import React from 'react';
import { FaMagic, <PERSON>a<PERSON>lock, Fa<PERSON><PERSON>, Fa<PERSON><PERSON><PERSON>, FaHeart } from 'react-icons/fa';

interface Service {
    id: number;
    name: string;
    description: string;
    duration_minutes: number;
    price: number;
    image: string;
    gender?: 'male' | 'female';
}

interface ServicesSectionProps {
    services: Service[];
    selectedServices: Service[];
    selectedGender: 'male' | 'female';
    setSelectedGender: (gender: 'male' | 'female') => void;
    addedServices: Record<number, boolean>;
    currency_symbol: string;
    addToCart: (service: Service) => void;
    servicesRef: React.RefObject<HTMLDivElement>;
}

const ServicesSection: React.FC<ServicesSectionProps> = ({
    services,
    selectedServices,
    selectedGender,
    setSelectedGender,
    addedServices,
    currency_symbol,
    addToCart,
    servicesRef
}) => {
    if (services.length === 0) {
        return null;
    }

    const filteredServices = services.filter(service => service.gender === selectedGender);

    return (
        <div ref={servicesRef} className="relative bg-white py-20 sm:py-20">
            <div className="mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
                {/* Section Header */}
                <div className="mb-16 text-center">
                    <div className="inline-flex justify-center items-center bg-orange-800 mb-6 rounded-2xl w-16 h-16">
                        <FaMagic className="w-8 h-8 text-white" />
                    </div>
                    <h2 className="mb-6 font-black text-4xl sm:text-5xl lg:text-6xl text-gray-900">
                        Our Services
                    </h2>
                    <p className="mx-auto max-w-3xl text-gray-700 text-xl leading-relaxed">
                        Discover our premium beauty services crafted by expert professionals
                    </p>
                </div>

                {/* Gender Tabs */}
                <div className="flex justify-center gap-4 mb-10">
                    <button
                        className={`px-6 py-3 rounded-full font-bold text-lg transition-all duration-200 border-2 focus:outline-none ${selectedGender === 'male' 
                            ? 'bg-orange-800 text-white border-orange-800 shadow-lg' 
                            : 'bg-white text-orange-800 border-orange-800/30 hover:bg-orange-50'}`}
                        onClick={() => setSelectedGender('male')}
                    >
                        Male
                    </button>
                    <button
                        className={`px-6 py-3 rounded-full font-bold text-lg transition-all duration-200 border-2 focus:outline-none ${selectedGender === 'female' 
                            ? 'bg-gray-800 text-white border-gray-800 shadow-lg' 
                            : 'bg-white text-gray-800 border-gray-800/30 hover:bg-gray-50'}`}
                        onClick={() => setSelectedGender('female')}
                    >
                        Female
                    </button>
                </div>

                {/* Services Grid */}
                <div className="gap-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 stagger-animation">
                    {filteredServices.length === 0 ? (
                        <div className="col-span-full py-12 font-semibold text-center text-gray-500 text-xl">
                            {selectedGender === 'male' ? 'No male services available.' : 'No female services available.'}
                        </div>
                    ) : (
                        <>
                            {filteredServices.map((service) => (
                                <div
                                    key={service.id}
                                    className="group relative flex flex-col justify-between bg-white hover:shadow-3xl border border-gray-200 rounded-3xl transition-all hover:-translate-y-3 duration-500 overflow-hidden service-card card-hover"
                                >
                                    {/* Service Content */}
                                    <div className="flex flex-col flex-1 gap-2 p-8">
                                        <div className="flex items-center gap-4 mb-2">
                                            <div className="bg-orange-800 p-3 rounded-2xl">
                                                <FaMagic className="w-7 h-7 text-white" />
                                            </div>
                                            <h3 className="font-bold text-2xl text-gray-900 group-hover:text-orange-800 transition-colors duration-300">
                                                {service.name}
                                            </h3>
                                        </div>
                                        <p className="mb-2 text-base text-gray-700 leading-relaxed line-clamp-4">
                                            {service.description}
                                        </p>
                                        <div className="flex items-center gap-4 mt-4">
                                            <div className="flex items-center gap-2 bg-orange-50 px-4 py-2 rounded-xl border border-orange-200">
                                                <FaClock className="w-4 h-4 text-orange-800" />
                                                <span className="font-medium text-gray-800 text-sm">{service.duration_minutes} mins</span>
                                            </div>
                                            <div className="flex items-center gap-2 bg-gray-50 px-4 py-2 rounded-xl border border-gray-200">
                                                <FaGem className="w-4 h-4 text-gray-800" />
                                                <span className="font-bold text-lg text-orange-800">{currency_symbol}{service.price}</span>
                                            </div>
                                        </div>
                                    </div>
                                    {/* Price and Action */}
                                    <div className="flex justify-between items-center px-6 py-4 border-gray-200 border-t">
                                        <span className="text-gray-600 text-sm">per session</span>
                                        <button
                                            id={`service-${service.id}`}
                                            onClick={() => addToCart(service)}
                                            disabled={selectedServices.some(s => s.id === service.id) || addedServices[service.id]}
                                            className={`btn-modern group/btn relative overflow-hidden px-6 py-3 rounded-2xl !shadow-none font-bold transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-orange-800/30 touch-target ${selectedServices.some(s => s.id === service.id) || addedServices[service.id]
                                                ? 'bg-green-600 text-white cursor-not-allowed'
                                                : 'bg-orange-800 hover:bg-orange-900 text-white hover:shadow-xl'
                                                }`}
                                        >
                                            <div className="relative flex justify-center items-center gap-2">
                                                {selectedServices.some(s => s.id === service.id) || addedServices[service.id] ? (
                                                    <>
                                                        <FaCheck className="w-4 h-4" />
                                                        <span>Added</span>
                                                    </>
                                                ) : (
                                                    <>
                                                        <FaHeart className="w-4 h-4 transition-transform duration-300 group-hover/btn:scale-110" />
                                                        <span>Add</span>
                                                    </>
                                                )}
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ServicesSection;
