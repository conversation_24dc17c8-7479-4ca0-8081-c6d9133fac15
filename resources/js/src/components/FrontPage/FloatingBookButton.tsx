import React from 'react';
import { FaShoppingCart, FaGem, FaCalendarAlt, FaArrowRight } from 'react-icons/fa';

interface Service {
    id: number;
    name: string;
    description: string;
    duration_minutes: number;
    price: number;
    image: string;
    gender?: 'male' | 'female';
}

interface FloatingBookButtonProps {
    selectedServices: Service[];
    currency_symbol: string;
    setShowCart: (show: boolean) => void;
    setIsBookingModalOpen: (open: boolean) => void;
    calculateTotal: () => number;
    calculateTotalDuration: () => number;
}

const FloatingBookButton: React.FC<FloatingBookButtonProps> = ({
    selectedServices,
    currency_symbol,
    setShowCart,
    setIsBookingModalOpen,
    calculateTotal,
    calculateTotalDuration
}) => {
    if (selectedServices.length === 0) {
        return null;
    }

    return (
        <div className="md:block right-4 bottom-4 left-4 z-50 fixed hidden">
            <div className="bg-white/95 shadow-2xl backdrop-blur-xl p-4 sm:p-6 border border-gray-200 rounded-3xl">
                <div className="flex sm:flex-row flex-col justify-between items-center gap-4">
                    <div className="text-center sm:text-left">
                        <div className="flex justify-center sm:justify-start items-center gap-2 mb-1">
                            <FaShoppingCart className="text-orange-800" />
                            <span className="font-medium text-gray-700">{selectedServices.length} services selected</span>
                        </div>
                        <div className="flex justify-center sm:justify-start items-center gap-2">
                            <FaGem className="text-orange-800 text-sm" />
                            <span className="font-bold text-orange-800 text-xl">{currency_symbol}{calculateTotal()}</span>
                            <span className="text-gray-600 text-sm">• {calculateTotalDuration()} mins</span>
                        </div>
                    </div>
                    <div className="flex items-center gap-3">
                        <button
                            onClick={() => setShowCart(true)}
                            className="inline-flex justify-center items-center bg-orange-50 hover:bg-orange-100 px-6 py-4 rounded-2xl focus:ring-4 focus:ring-orange-800/30 font-bold text-lg text-orange-800 transition-all duration-300 focus:outline-none border border-orange-200"
                        >
                            <FaShoppingCart className="mr-2 w-5 h-5" />
                            View Cart
                        </button>
                        <button
                            onClick={() => setIsBookingModalOpen(true)}
                            className="inline-flex justify-center items-center bg-orange-800 hover:bg-orange-900 hover:shadow-xl px-8 py-4 rounded-2xl focus:ring-4 focus:ring-orange-800/30 font-bold text-lg text-white transform transition-all hover:-translate-y-1 duration-300 focus:outline-none hover:scale-105"
                        >
                            <FaCalendarAlt className="mr-3 w-5 h-5" />
                            Book Now
                            <FaArrowRight className="ml-3 w-4 h-4" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FloatingBookButton;
