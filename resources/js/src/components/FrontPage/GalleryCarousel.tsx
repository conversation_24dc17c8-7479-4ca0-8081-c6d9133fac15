import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';

interface BranchMedia {
    id: number;
    image: string;
    type: string;
    status: boolean;
}

interface GalleryCarouselProps {
    galleryImages: BranchMedia[];
}

const GalleryCarousel: React.FC<GalleryCarouselProps> = ({ galleryImages }) => {
    if (galleryImages.length === 0) {
        return null;
    }

    return (
        <div className="mx-auto px-4 sm:px-6 lg:px-8 py-12 max-w-7xl">
            <Swiper
                modules={[Navigation, Pagination]}
                navigation
                pagination={{ clickable: true }}
                breakpoints={{
                    1024: { slidesPerView: 3, spaceBetween: 30 },
                    768: { slidesPerView: 2, spaceBetween: 20 },
                    320: { slidesPerView: 1, spaceBetween: 10 },
                }}
                className="w-full swiper"
            >
                {galleryImages.map((img) => (
                    <SwiperSlide key={img.id}>
                        <img
                            src={`/${img.image}`}
                            alt="Gallery"
                            className="shadow-lg rounded-2xl w-full h-64 object-cover"
                        />
                    </SwiperSlide>
                ))}
            </Swiper>
        </div>
    );
};

export default GalleryCarousel;
