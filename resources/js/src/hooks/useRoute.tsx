import { usePage } from '@inertiajs/react';

interface ZiggyRoute {
  uri: string;
  methods: string[];
  parameters?: string[];
}

interface ZiggyConfig {
  url: string;
  port: number | null;
  defaults: Record<string, any>;
  routes: Record<string, ZiggyRoute>;
}

// Hook to provide Ziggy route functionality
export const useRoute = () => {
  const { props } = usePage();
  const ziggy = props.ziggy as ZiggyConfig;

  const route = (name: string, params: Record<string, any> = {}) => {
    if (!ziggy || !ziggy.routes || !ziggy.routes[name]) {
      console.warn(`Route "${name}" not found`);
      return '/';
    }

    const routeConfig = ziggy.routes[name];
    let uri = routeConfig.uri;

    // Replace route parameters
    Object.keys(params).forEach(key => {
      uri = uri.replace(`{${key}}`, params[key]);
      uri = uri.replace(`{${key}?}`, params[key] || '');
    });

    // Remove any remaining optional parameters
    uri = uri.replace(/\{[^}]+\?\}/g, '');

    // Ensure the URI starts with a slash
    if (!uri.startsWith('/')) {
      uri = '/' + uri;
    }

    return uri;
  };

  return { route };
};
