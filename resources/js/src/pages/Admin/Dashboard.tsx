import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';
import { Link } from '@inertiajs/react';

interface Props {
    totalVendors: number;
    pendingVendors: number;
    totalPlans: number;
    totalRevenue: number;
}

const AdminDashboard = ({
    totalVendors = 0,
    pendingVendors = 0,
    totalPlans = 0,
    totalRevenue = 0,
}: Props) => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Admin Dashboard'));
    }, [dispatch]);

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse mb-4">
                <li>
                    <Link href="/" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Admin Dashboard</span>
                </li>
            </ul>

            <div className="pt-5">
                {/* Metrics Overview */}
                <div className="gap-6 grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 mb-6">
                    <div className="bg-gradient-to-r from-blue-500 to-blue-400 panel">
                        <div className="flex justify-between">
                            <div className="font-semibold text-md text-white">Total Vendors</div>
                        </div>
                        <div className="flex items-center mt-5">
                            <div className="font-bold text-3xl text-white">{totalVendors}</div>
                        </div>
                    </div>
                    <div className="bg-gradient-to-r from-yellow-500 to-yellow-400 panel">
                        <div className="flex justify-between">
                            <div className="font-semibold text-md text-white">Pending Vendors</div>
                        </div>
                        <div className="flex items-center mt-5">
                            <div className="font-bold text-3xl text-white">{pendingVendors}</div>
                        </div>
                    </div>
                    <div className="bg-gradient-to-r from-green-500 to-green-400 panel">
                        <div className="flex justify-between">
                            <div className="font-semibold text-md text-white">Subscription Plans</div>
                        </div>
                        <div className="flex items-center mt-5">
                            <div className="font-bold text-3xl text-white">{totalPlans}</div>
                        </div>
                    </div>
                    <div className="bg-gradient-to-r from-fuchsia-500 to-fuchsia-400 panel">
                        <div className="flex justify-between">
                            <div className="font-semibold text-md text-white">Total Revenue</div>
                        </div>
                        <div className="flex items-center mt-5">
                            <div className="font-bold text-3xl text-white">₹{totalRevenue.toLocaleString(undefined, { minimumFractionDigits: 2 })}</div>
                        </div>
                    </div>
                </div>

                {/* Placeholder for future charts or tables */}
                <div className="panel mb-6">
                    <div className="flex justify-between items-center mb-5 dark:text-white-light">
                        <h5 className="font-semibold text-lg">Overview</h5>
                    </div>
                    <div className="text-gray-500 dark:text-gray-400">
                        {/* Add charts, tables, or recent activity here in the future */}
                        <p>Welcome to the Admin Dashboard. Here you can monitor vendor registrations, subscriptions, and revenue at a glance.</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AdminDashboard; 