import React, { useEffect } from 'react';
import { Link, router, usePage } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';
import Swal from 'sweetalert2';

interface Plan {
    id: number;
    name: string;
    description: string;
    price: number;
    billing_cycle: string;
    max_services: number;
    max_branches: number;
    max_staff: number;
    is_active: boolean;
    sort_order: number;
    currency_symbol: string;
    currency_code: string;
}

interface Props {
    plans: Plan[];
    trashedCount: number;
    flash?: { success?: string; error?: string };
}

const Index = ({ plans, trashedCount, flash }: Props) => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Subscription Plans'));
    }, [dispatch]);

    useEffect(() => {
        if (flash?.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash?.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash?.error, flash?.success]);

    const handleDelete = (id: number) => {
        Swal.fire({
            title: 'Are you sure?',
            html: 'Are you sure you want to delete this subscription plan?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.delete(route('siteadmin.subscription-plans.destroy', { subscription_plan: id }), {
                    preserveScroll: true,
                });
            }
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Subscription Plans</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="flex items-center justify-between mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Subscription Plans</h5>
                        <div className="flex gap-2">
                            <Link
                                href={route('siteadmin.subscription-plans.trashed')}
                                className={`btn btn-outline-secondary relative ${trashedCount === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                                disabled={trashedCount === 0}
                            >
                                Trashed Plans
                                {trashedCount > 0 && (
                                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                        {trashedCount}
                                    </span>
                                )}
                            </Link>
                            <Link href={route('siteadmin.subscription-plans.create')} className="btn btn-primary">
                                + Add New Plan
                            </Link>
                        </div>
                    </div>
                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Currency</th>
                                    <th>Price</th>
                                    <th>Billing Cycle</th>
                                    <th>Max Services</th>
                                    <th>Max Branches</th>
                                    <th>Max Staff</th>
                                    <th>Status</th>
                                    <th>Sort Order</th>
                                    <th className="!text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {plans.map((plan) => (
                                    <tr key={plan.id}>
                                        <td>{plan.name}</td>
                                        <td>{plan.description}</td>
                                        <td>{plan.currency_code} ({plan.currency_symbol})</td>
                                        <td>{plan.currency_symbol}{Number(plan.price).toFixed(2)}</td>
                                        <td>{plan.billing_cycle.charAt(0).toUpperCase() + plan.billing_cycle.slice(1)}</td>
                                        <td>{plan.max_services === 0 ? 'Unlimited' : plan.max_services}</td>
                                        <td>{plan.max_branches}</td>
                                        <td>{plan.max_staff === 0 ? 'Unlimited' : plan.max_staff}</td>
                                        <td>
                                            <span className={`badge badge-outline-${plan.is_active ? 'success' : 'danger'}`}>
                                                {plan.is_active ? 'Active' : 'Inactive'}
                                            </span>
                                        </td>
                                        <td>{plan.sort_order}</td>
                                        <td className="text-center">
                                            <div className="flex gap-2 items-center justify-center">
                                                <Link href={route('siteadmin.subscription-plans.edit', { subscription_plan: plan.id })} className="btn btn-sm btn-outline-primary">
                                                    Edit
                                                </Link>
                                                <button type="button" className="btn btn-sm btn-outline-danger" onClick={() => handleDelete(plan.id)}>
                                                    Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Index;
