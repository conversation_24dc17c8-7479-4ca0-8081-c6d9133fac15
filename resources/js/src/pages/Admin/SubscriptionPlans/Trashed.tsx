import React, { useEffect } from 'react';
import { Link, router, usePage } from '@inertiajs/react';
import Swal from 'sweetalert2';

interface Plan {
    id: number;
    name: string;
    description: string;
    price: number;
    billing_cycle: string;
    max_services: number;
    max_branches: number;
    max_staff: number;
    is_active: boolean;
    sort_order: number;
    currency_symbol: string;
    currency_code: string;
}

interface Props {
    plans: Plan[];
    flash?: { success?: string; error?: string };
}

const Trashed = ({ plans, flash }: Props) => {
    useEffect(() => {
        if (flash?.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash?.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash?.error, flash?.success]);

    const handleRestore = (id: number) => {
        Swal.fire({
            title: 'Restore Plan?',
            html: 'Are you sure you want to restore this subscription plan?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, restore it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.put(route('siteadmin.subscription-plans.restore', { subscription_plan: id }), {}, {
                    preserveScroll: true,
                });
            }
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href={route('siteadmin.subscription-plans.index')} className="text-primary hover:underline">
                        Subscription Plans
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Trashed</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="flex items-center justify-between mb-5">
                        <div>
                            <h5 className="font-semibold text-lg dark:text-white-light">Trashed Subscription Plans</h5>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                {plans.length} trashed {plans.length === 1 ? 'plan' : 'plans'}
                            </p>
                        </div>
                        <Link href={route('siteadmin.subscription-plans.index')} className="btn btn-outline-primary">
                            Back to Active Plans
                        </Link>
                    </div>
                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Currency</th>
                                    <th>Price</th>
                                    <th>Billing Cycle</th>
                                    <th>Max Services</th>
                                    <th>Max Branches</th>
                                    <th>Max Staff</th>
                                    <th>Status</th>
                                    <th>Sort Order</th>
                                    <th className="!text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {plans.map((plan) => (
                                    <tr key={plan.id}>
                                        <td>{plan.name}</td>
                                        <td>{plan.description}</td>
                                        <td>{plan.currency_code} ({plan.currency_symbol})</td>
                                        <td>{plan.currency_symbol}{Number(plan.price).toFixed(2)}</td>
                                        <td>{plan.billing_cycle.charAt(0).toUpperCase() + plan.billing_cycle.slice(1)}</td>
                                        <td>{plan.max_services === 0 ? 'Unlimited' : plan.max_services}</td>
                                        <td>{plan.max_branches}</td>
                                        <td>{plan.max_staff === 0 ? 'Unlimited' : plan.max_staff}</td>
                                        <td>
                                            <span className={`badge badge-outline-${plan.is_active ? 'success' : 'danger'}`}>
                                                {plan.is_active ? 'Active' : 'Inactive'}
                                            </span>
                                        </td>
                                        <td>{plan.sort_order}</td>
                                        <td className="text-center">
                                            <button type="button" className="btn btn-sm btn-outline-success" onClick={() => handleRestore(plan.id)}>
                                                Restore
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Trashed;
