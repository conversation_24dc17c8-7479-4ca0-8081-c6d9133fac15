import React, { useEffect } from 'react';
import { Link, router, usePage } from '@inertiajs/react';
import Swal from 'sweetalert2';

interface Subscription {
    id: number;
    user: { id: number; name: string; email: string };
    subscription_plan: { id: number; name: string; price: number; currency_symbol: string; currency_code: string };
    status: string;
    starts_at: string;
    ends_at: string;
}

interface Props {
    subscriptions: Subscription[];
    trashedCount: number;
    flash?: { success?: string; error?: string };
}

const Index = ({ subscriptions, trashedCount, flash }: Props) => {
    useEffect(() => {
        if (flash?.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash?.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash?.error, flash?.success]);

    const handleDelete = (id: number) => {
        Swal.fire({
            title: 'Are you sure?',
            html: 'Are you sure you want to delete this purchased plan?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.delete(route('siteadmin.vendor-subscriptions.destroy', { vendor_subscription: id }), {
                    preserveScroll: true,
                });
            }
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Purchased Plans</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="flex items-center justify-between mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Purchased Plans</h5>
                        <Link
                            href={route('siteadmin.vendor-subscriptions.trashed')}
                            className={`btn btn-outline-secondary relative ${trashedCount === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                            disabled={trashedCount === 0}
                        >
                            View Trashed
                            {trashedCount > 0 && (
                                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                    {trashedCount}
                                </span>
                            )}
                        </Link>
                    </div>
                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Vendor</th>
                                    <th>Email</th>
                                    <th>Plan</th>
                                    <th>Price</th>
                                    <th>Currency</th>
                                    <th>Status</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th className="!text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {subscriptions.map((sub) => (
                                    <tr key={sub.id}>
                                        <td>{sub.id}</td>
                                        <td>{sub.user?.name}</td>
                                        <td>{sub.user?.email}</td>
                                        <td>{sub.subscription_plan?.name}</td>
                                        <td>{sub.subscription_plan?.currency_symbol}{Number(sub.subscription_plan?.price).toFixed(2)}</td>
                                        <td>{sub.subscription_plan?.currency_code}</td>
                                        <td>
                                            <span className={`badge badge-outline-${sub.status === 'active' ? 'success' : sub.status === 'trial' ? 'info' : sub.status === 'cancelled' ? 'danger' : 'warning'}`}>
                                                {sub.status.charAt(0).toUpperCase() + sub.status.slice(1)}
                                            </span>
                                        </td>
                                        <td>{sub.starts_at ? new Date(sub.starts_at).toLocaleDateString() : '-'}</td>
                                        <td>{sub.ends_at ? new Date(sub.ends_at).toLocaleDateString() : '-'}</td>
                                        <td className="text-center">
                                            <div className="flex gap-2 items-center justify-center">
                                                <Link href={route('siteadmin.vendor-subscriptions.edit', { vendor_subscription: sub.id })} className="btn btn-sm btn-outline-primary">
                                                    Edit
                                                </Link>
                                                <button type="button" className="btn btn-sm btn-outline-danger" onClick={() => handleDelete(sub.id)}>
                                                    Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Index;
