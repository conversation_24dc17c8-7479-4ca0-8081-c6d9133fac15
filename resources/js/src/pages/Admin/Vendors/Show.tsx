// resources/js/src/pages/Admin/Vendors/Show.tsx
import React from 'react';
import { Link } from '@inertiajs/react';

interface Subscription {
    id: number;
    subscription_plan: { name: string };
    status: string;
    starts_at: string;
    ends_at: string;
}

interface Vendor {
    id: number;
    name: string;
    email: string;
}

interface Props {
    vendor: Vendor;
    subscriptions: Subscription[];
}

const Show = ({ vendor, subscriptions }: Props) => (
    <div>
        <ul className="flex space-x-2 rtl:space-x-reverse">
            <li>
                <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                    Dashboard
                </Link>
            </li>
            <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                <Link href={route('siteadmin.vendors.index')} className="text-primary hover:underline">
                    Manage Vendors
                </Link>
            </li>
            <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                <span>Vendor Details</span>
            </li>
        </ul>
        <div className="pt-5">
            <div className="panel mb-6">
                <h5 className="font-semibold text-lg dark:text-white-light mb-4">Vendor Info</h5>
                <div>Name: {vendor.name}</div>
                <div>Email: {vendor.email}</div>
            </div>
            <div className="panel">
                <h5 className="font-semibold text-lg dark:text-white-light mb-4">Purchased Plans</h5>
                <div className="table-responsive">
                    <table className="table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Plan</th>
                                <th>Status</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {subscriptions.map((sub) => (
                                <tr key={sub.id}>
                                    <td>{sub.id}</td>
                                    <td>{sub.subscription_plan?.name}</td>
                                    <td>
                                        <span className={`badge badge-outline-${sub.status === 'active' ? 'success' : sub.status === 'trial' ? 'info' : sub.status === 'cancelled' ? 'danger' : 'warning'}`}>
                                            {sub.status.charAt(0).toUpperCase() + sub.status.slice(1)}
                                        </span>
                                    </td>
                                    <td>{sub.starts_at ? new Date(sub.starts_at).toLocaleDateString() : '-'}</td>
                                    <td>{sub.ends_at ? new Date(sub.ends_at).toLocaleDateString() : '-'}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
);

export default Show;

