import React, { useEffect } from 'react';
import { Link, router } from '@inertiajs/react';
import Swal from 'sweetalert2';

interface Vendor {
    id: number;
    name: string;
    email: string;
    is_active?: boolean;
}

interface Props {
    vendors: Vendor[];
    flash?: { success?: string; error?: string };
}

const Index = ({ vendors, flash }: Props) => {
    useEffect(() => {
        if (flash?.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash?.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash?.error, flash?.success]);

    const handleDelete = (id: number) => {
        Swal.fire({
            title: 'Are you sure?',
            html: 'Are you sure you want to delete this vendor?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.delete(route('siteadmin.vendors.destroy', { vendor: id }), {
                    preserveScroll: true,
                });
            }
        });
    };

    const handleAccessPortal = (id: number) => {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = route('siteadmin.vendors.access-portal', { vendor: id });

        // Add CSRF token if needed
        const csrf = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrf) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = '_token';
            input.value = csrf;
            form.appendChild(input);
        }

        document.body.appendChild(form);
        form.submit();
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Manage Vendors</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="flex items-center justify-between mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Vendors</h5>
                    </div>
                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Status</th>
                                    <th className="!text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {vendors.map((vendor) => (
                                    <tr key={vendor.id}>
                                        <td>{vendor.id}</td>
                                        <td>{vendor.name}</td>
                                        <td>{vendor.email}</td>
                                        <td>
                                            <span className={`badge badge-outline-${vendor.is_active ? 'success' : 'danger'}`}>
                                                {vendor.is_active ? 'Active' : 'Inactive'}
                                            </span>
                                        </td>
                                        <td className="text-center">
                                            <div className="flex gap-2 items-center justify-center">
                                                <Link href={route('siteadmin.vendors.show', { vendor: vendor.id })} className="btn btn-sm btn-outline-info">
                                                    View
                                                </Link>
                                                {/* <Link href={route('siteadmin.vendors.edit', { vendor: vendor.id })} className="btn btn-sm btn-outline-primary">
                                                    Edit
                                                </Link>
                                                <button type="button" className="btn btn-sm btn-outline-danger" onClick={() => handleDelete(vendor.id)}>
                                                    Delete
                                                </button> */}
                                                <button type="button" className="btn btn-sm btn-outline-success" onClick={() => handleAccessPortal(vendor.id)}>
                                                    Access Portal
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Index;