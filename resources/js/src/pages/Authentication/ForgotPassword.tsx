import { useEffect, useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch, useSelector } from 'react-redux';
import { IRootState } from '@store';
import { setPageTitle } from '@store/themeConfigSlice';

const ForgotPassword = ({ status, errors = {} }) => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Forgot Password'));
    });

    const [formData, setFormData] = useState({
        email: '',
    });

    const [processing, setProcessing] = useState(false);

    const handleChange = (e) => {
        const { id, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [id]: value
        }));
    };

    const submitForm = (e) => {
        e.preventDefault();
        setProcessing(true);
        router.post('/auth/forgot-password', formData, {
            onFinish: () => setProcessing(false)
        });
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
            {/* Subtle animated background elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-20 left-10 w-32 h-32 bg-orange-800/5 rounded-full animate-float-slow"></div>
                <div className="absolute top-40 right-20 w-24 h-24 bg-gray-400/5 rounded-full animate-float-medium"></div>
                <div className="absolute bottom-40 left-20 w-40 h-40 bg-orange-800/5 rounded-full animate-float-slow"></div>
                <div className="absolute bottom-20 right-10 w-28 h-28 bg-gray-400/5 rounded-full animate-float-fast"></div>
            </div>

            <div className="flex min-h-screen items-center justify-center px-4 py-8">
                <div className="w-full max-w-md">
                    <div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/20 p-8">
                        {/* Decorative gradient border */}
                        <div className="absolute inset-0 rounded-3xl blur-xl"></div>
                        
                        <div className="relative">
                            {/* Header Section */}
                            <div className="text-center mb-8">
                                <h1 className="text-3xl font-black text-gray-900 dark:text-white mb-2">
                                    Forgot Password?
                                </h1>
                                <p className="text-gray-600 dark:text-gray-400 font-medium">
                                    Enter your email address and we'll send you a link to reset your password
                                </p>
                            </div>

                            {status && (
                                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-2xl p-4 mb-6">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                        <div className="ml-3">
                                            <p className="text-green-800 dark:text-green-200 font-medium">{status}</p>
                                        </div>
                                    </div>
                                </div>
                            )}

                            <form className="space-y-6" onSubmit={submitForm}>
                                <div className="space-y-2">
                                    <label htmlFor="email" className="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                        Email Address
                                    </label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                                            </svg>
                                        </div>
                                        <input
                                            id="email"
                                            type="email"
                                            placeholder="Enter your email address"
                                            className="w-full pl-12 pr-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-4 focus:ring-orange-800/20 focus:border-orange-800 dark:focus:border-orange-600 transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                                            value={formData.email}
                                            onChange={handleChange}
                                            required
                                        />
                                    </div>
                                    {errors.email && (
                                        <p className="text-red-600 dark:text-red-400 text-sm font-medium">{errors.email}</p>
                                    )}
                                </div>

                                <button
                                    type="submit"
                                    className="w-full mt-8 px-6 py-3 bg-gradient-to-r from-orange-800 to-orange-900 hover:from-orange-900 hover:to-orange-800 text-white font-bold rounded-2xl shadow-lg hover:shadow-xl transform transition-all duration-300 hover:scale-[1.02] focus:outline-none focus:ring-4 focus:ring-orange-800/30 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={processing}
                                >
                                    <div className="flex items-center justify-center">
                                        {processing ? (
                                            <>
                                                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                Sending Reset Link...
                                            </>
                                        ) : (
                                            <>
                                                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                                </svg>
                                                Send Reset Link
                                            </>
                                        )}
                                    </div>
                                </button>

                                <div className="text-center mt-6">
                                    <Link
                                        href="/auth/login"
                                        className="text-sm font-semibold text-orange-800 hover:text-orange-900 dark:text-orange-600 dark:hover:text-orange-500 transition-colors duration-200 hover:underline"
                                    >
                                        ← Back to Login
                                    </Link>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;
