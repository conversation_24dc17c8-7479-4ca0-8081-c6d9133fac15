import { useEffect } from 'react';
import { Link, useForm } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';

const Create = () => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Create Staff Member'));
    });

    const { data, setData, post, processing, errors } = useForm({
        name: '',
        email: '',
        phone: '',
        gender: '',
        address: '',
        password: '',
        password_confirmation: '',
        is_active: true,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/vendor/staff');
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/staff" className="text-primary hover:underline">
                        Staff
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Create</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Create New Staff Member</h5>
                    </div>

                    <form className="space-y-5" onSubmit={handleSubmit}>
                        <div>
                            <label htmlFor="name">Name</label>
                            <input
                                id="name"
                                type="text"
                                placeholder="Enter Name"
                                className="form-input"
                                value={data.name}
                                onChange={(e) => setData('name', e.target.value)}
                            />
                            {errors.name && <div className="text-danger mt-1">{errors.name}</div>}
                        </div>

                        <div>
                            <label htmlFor="email">Email</label>
                            <input
                                id="email"
                                type="email"
                                placeholder="Enter Email"
                                className="form-input"
                                value={data.email}
                                onChange={(e) => setData('email', e.target.value)}
                            />
                            {errors.email && <div className="text-danger mt-1">{errors.email}</div>}
                        </div>

                        <div>
                            <label htmlFor="phone">Phone</label>
                            <input
                                id="phone"
                                type="tel"
                                placeholder="Enter Phone Number"
                                className="form-input"
                                value={data.phone}
                                onChange={(e) => setData('phone', e.target.value)}
                            />
                            {errors.phone && <div className="text-danger mt-1">{errors.phone}</div>}
                        </div>

                        <div>
                            <label htmlFor="gender">Gender</label>
                            <select
                                id="gender"
                                className="form-select"
                                value={data.gender}
                                onChange={(e) => setData('gender', e.target.value)}
                            >
                                <option value="">Select Gender</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                            </select>
                            {errors.gender && <div className="text-danger mt-1">{errors.gender}</div>}
                        </div>

                        <div>
                            <label htmlFor="address">Address</label>
                            <textarea
                                id="address"
                                rows={3}
                                className="form-textarea resize-none min-h-[130px]"
                                placeholder="Enter Address"
                                value={data.address}
                                onChange={(e) => setData('address', e.target.value)}
                            ></textarea>
                            {errors.address && <div className="text-danger mt-1">{errors.address}</div>}
                        </div>

                        <div>
                            <label htmlFor="password">Password</label>
                            <input
                                id="password"
                                type="password"
                                placeholder="Enter Password"
                                className="form-input"
                                value={data.password}
                                onChange={(e) => setData('password', e.target.value)}
                            />
                            {errors.password && <div className="text-danger mt-1">{errors.password}</div>}
                        </div>

                        <div>
                            <label htmlFor="password_confirmation">Confirm Password</label>
                            <input
                                id="password_confirmation"
                                type="password"
                                placeholder="Confirm Password"
                                className="form-input"
                                value={data.password_confirmation}
                                onChange={(e) => setData('password_confirmation', e.target.value)}
                            />
                            {errors.password_confirmation && <div className="text-danger mt-1">{errors.password_confirmation}</div>}
                        </div>

                        <div>
                            <label className="inline-flex">
                                <input
                                    type="checkbox"
                                    className="form-checkbox"
                                    checked={data.is_active}
                                    onChange={(e) => setData('is_active', e.target.checked)}
                                />
                                <span>Active</span>
                            </label>
                            {errors.is_active && <div className="text-danger mt-1">{errors.is_active}</div>}
                        </div>

                        <div className="flex justify-end items-center mt-8">
                            <Link href="/vendor/staff" className="btn btn-outline-danger">
                                Cancel
                            </Link>
                            <button type="submit" className="btn btn-primary ltr:ml-4 rtl:mr-4" disabled={processing}>
                                {processing ? 'Creating...' : 'Create Staff Member'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Create; 