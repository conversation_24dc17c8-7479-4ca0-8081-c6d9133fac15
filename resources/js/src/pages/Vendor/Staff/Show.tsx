import { useEffect } from 'react';
import { Link } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';

interface Props {
    staff: {
        id: number;
        name: string;
        email: string;
        phone: string;
        gender: string;
        address: string | null;
        is_active: boolean;
    };
}

const Show = ({ staff }: Props) => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Staff Member Details'));
    });

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/staff" className="text-primary hover:underline">
                        Staff
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Details</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="flex items-center justify-between mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Staff Member Details</h5>
                        <div className="flex items-center gap-4">
                            <Link href={`/vendor/staff/${staff.id}/edit`} className="btn btn-primary">
                                Edit
                            </Link>
                            <Link href="/vendor/staff" className="btn btn-outline-danger">
                                Back to List
                            </Link>
                        </div>
                    </div>

                    <div className="space-y-5">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                            <div>
                                <h6 className="text-gray-500 dark:text-gray-400 mb-2">Name</h6>
                                <p className="text-base">{staff.name}</p>
                            </div>
                            <div>
                                <h6 className="text-gray-500 dark:text-gray-400 mb-2">Email</h6>
                                <p className="text-base">{staff.email}</p>
                            </div>
                            <div>
                                <h6 className="text-gray-500 dark:text-gray-400 mb-2">Phone</h6>
                                <p className="text-base">{staff.phone}</p>
                            </div>
                            <div>
                                <h6 className="text-gray-500 dark:text-gray-400 mb-2">Gender</h6>
                                <p className="text-base capitalize">{staff.gender}</p>
                            </div>
                            <div>
                                <h6 className="text-gray-500 dark:text-gray-400 mb-2">Status</h6>
                                <span className={`badge badge-outline-${staff.is_active ? 'success' : 'danger'}`}>
                                    {staff.is_active ? 'Active' : 'Inactive'}
                                </span>
                            </div>
                        </div>

                        {staff.address && (
                            <div>
                                <h6 className="text-gray-500 dark:text-gray-400 mb-2">Address</h6>
                                <p className="text-base whitespace-pre-line">{staff.address}</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Show; 