import React, { useState, Fragment, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Link, usePage, router } from '@inertiajs/react';
import Swal from 'sweetalert2';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';

interface Service {
    id: number;
    name: string;
    duration_minutes: number;
    price: number;
    estimated_end_time?: string;
    start_time?: string;
    seat_name?: string;
    staff_name?: string;
}

interface User {
    id: number;
    name: string;
    email: string;
    phone: string;
}

interface Staff {
    id: number;
    name: string;
    email: string;
    phone: string;
}


interface AppoinmentPlanUsages{
    name:string;
    service_name:string;
    service_id:number;
}

interface AppoinmentServices{
    appointment_service:string;
    appointment_service_price:number;
    appointment_service_service_id:number;
    appointment_service_notes:string;
}

interface AppointmentData {
    id: number;
    user: User;
    appointment_date: string;
    appointment_time: string;
    currency_symbol: string;
    currency_text: string;
    ticket_number: string;
    appoinmentUsedPlan:AppoinmentPlanUsages[];
    appointmentServices:AppoinmentServices[];
    app_price:number;
    status: string;
    services: Service[];
    notes: string;
    total_estimated_end_time?: string;
    total_duration_minutes?: number;
    earliest_start_time?: string;
    staff_json?: string;
}

interface PageProps {
    appointments: {
        data: AppointmentData[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        links?: any[];
    };
    filters: {
        search?: string;
        status?: string;
        date?: string;
        sort?: string;
        direction?: string;
    };
    [key: string]: any;
}

const Appointment = () => {
    const dispatch = useDispatch();
    const { appointments, filters, flash = {} } = usePage<PageProps & { flash?: { error?: string, success?: string } }>().props;

    useEffect(() => {
        dispatch(setPageTitle('Appointment'));
    });

    // Show SweetAlert for flash error
    useEffect(() => {
        if (flash.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash.error, flash.success]);

    const [value, setValue] = useState<any>('grid');
    const [search, setSearch] = useState<string>(filters.search || '');
    const [status, setStatus] = useState<string>(filters.status ?? 'pending');
    const [date, setDate] = useState<string>(filters.date || '');

    // Add this useEffect to reset filters if all are empty
    useEffect(() => {
        if (!filters.search && !filters.status && !filters.date) {
            setSearch('');
            setStatus('pending');
            setDate('');
        }
    }, [filters]);

    // Helper function to format status
    const formatStatus = (status: string) => {
        return status.split('_').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    };

    // Add formatDuration helper function
    const formatDuration = (minutes: number): string => {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        
        if (hours === 0) {
            return `${remainingMinutes}m`;
        }
        
        return `${hours}h ${remainingMinutes > 0 ? `${remainingMinutes}m` : ''}`;
    };

    // Transform appointment data for display
    const appointmentList = appointments.data.map((appointment) => ({
        id: appointment.id,
        name: appointment.user.name,
        email: appointment.user.email,
        phone: appointment.user.phone,
        appointment_date: appointment.appointment_date,
        appointment_time: appointment.appointment_time,
        currency_symbol: appointment.currency_symbol,
        app_price: appointment.app_price,
        currency_text: appointment.currency_text,
        ticket_number: appointment.ticket_number,
        status: appointment.status,
        services: appointment.services,
        notes: appointment.notes,
        staff_json: appointment.staff_json,
        appoinmentUsedPlan: appointment.appoinmentUsedPlan,
        appointmentServices: appointment.appointmentServices,
    }));


    const [filteredItems, setFilteredItems] = useState<any>(appointmentList);

    // Update filteredItems when appointments change
    useEffect(() => {
        setFilteredItems(appointmentList);
    }, [appointments]);

    // Handle search and filter changes
    const handleSearch = (value: string) => {
        setSearch(value);
        router.get(
            '/vendor/appointments',
            { search: value, status, date },
            { 
                preserveState: true, 
                preserveScroll: true,
                replace: true,
                only: ['appointments', 'filters']
            }
        );
    };

    const handleStatusChange = (value: string) => {
        setStatus(value);
        router.get(
            '/vendor/appointments',
            { search, status: value, date },
            { 
                preserveState: true, 
                preserveScroll: true,
                replace: true,
                only: ['appointments', 'filters']
            }
        );
    };

    const handleDateChange = (value: string) => {
        setDate(value);
        router.get(
            '/vendor/appointments',
            { search, status, date: value },
            { 
                preserveState: true, 
                preserveScroll: true,
                replace: true,
                only: ['appointments', 'filters']
            }
        );
    };

    const showMessage = (msg = '', type = 'success') => {
        const toast: any = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            customClass: { container: 'toast' },
        });
        toast.fire({
            icon: type,
            title: msg,
            padding: '10px 20px',
        });
    };

    const handleDelete = (appointment: any) => {
        Swal.fire({
            title: 'Are you sure?',
            html: `Are you sure you want to delete this professional appointment for <strong>${appointment.name}</strong>?<br>This action cannot be undone.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.delete(`/vendor/appointments/${appointment.id}`, {
                    onSuccess: () => {
                        // Do NOT show success here, let the useEffect for flash.success handle it
                    },
                    // onError is not used for this type of error
                });
            }
        });
    };

    // Add CountdownTimer component
    const CountdownTimer = ({ estimatedEndTime }: { estimatedEndTime: string }) => {
        const [timeLeft, setTimeLeft] = useState<string>('');

        useEffect(() => {
            const calculateTimeLeft = () => {
                const endTime = new Date(estimatedEndTime).getTime();
                const now = new Date().getTime();
                const difference = endTime - now;

                if (difference <= 0) {
                    setTimeLeft('Time is up');
                    return;
                }

                const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((difference % (1000 * 60)) / 1000);

                setTimeLeft(`${minutes}m ${seconds}s`);
            };

            calculateTimeLeft();
            const timer = setInterval(calculateTimeLeft, 1000);

            return () => clearInterval(timer);
        }, [estimatedEndTime]);

        return (
            <div className="text-xs font-semibold">
                <span className="text-warning">⏱️ {timeLeft}</span>
            </div>
        );
    };

    return (
        <div>
            <div className="flex sm:flex-row flex-col justify-center sm:justify-between items-center mb-4">
                <div className="mb-4 sm:mb-0">
                    <div className="rtl:sm:text-right font-semibold text-center text-lg ltr:sm:text-left">Appointments</div>
                </div>
                <div className="flex flex-wrap gap-4">
                    <Link href="/vendor/appointments/calendar" className="btn btn-primary">
                        <svg className="w-5 h-5 ltr:mr-2 rtl:ml-2" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="16" y1="2" x2="16" y2="6"></line>
                            <line x1="8" y1="2" x2="8" y2="6"></line>
                            <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                        Calendar View
                    </Link>
                    <Link href="/vendor/appointments/seat-map" className="btn btn-primary">
                        <svg className="w-5 h-5 ltr:mr-2 rtl:ml-2" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z" />
                            <circle cx="12" cy="10" r="3" />
                        </svg>
                        Seat Map View
                    </Link>
                    <Link href="/vendor/appointments/create" className="btn btn-primary">
                        <svg className="w-5 h-5 ltr:mr-2 rtl:ml-2" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                        Create Appointment
                    </Link>
                </div>
            </div>
            <div className="flex flex-wrap justify-between items-center gap-4">
                
                <div className="flex sm:flex-row flex-col sm:items-center gap-4 sm:gap-3 w-full sm:w-auto">
                    <div className="flex gap-3">
                       
                        <div>
                            <button type="button" className={`btn btn-outline-primary p-2 ${value === 'list' && 'bg-primary text-white'}`} onClick={() => setValue('list')}>
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-5 h-5">
                                    <path d="M2 5.5L3.21429 7L7.5 3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                    <path opacity="0.5" d="M2 12.5L3.21429 14L7.5 10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M2 19.5L3.21429 21L7.5 17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M22 19L12 19" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                    <path opacity="0.5" d="M22 12L12 12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                    <path d="M22 5L12 5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                </svg>
                            </button>
                        </div>
                        <div>
                            <button type="button" className={`btn btn-outline-primary p-2 ${value === 'grid' && 'bg-primary text-white'}`} onClick={() => setValue('grid')}>
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-5 h-5">
                                    <path
                                        opacity="0.5"
                                        d="M2.5 6.5C2.5 4.61438 2.5 3.67157 3.08579 3.08579C3.67157 2.5 4.61438 2.5 6.5 2.5C8.38562 2.5 9.32843 2.5 9.91421 3.08579C10.5 3.67157 10.5 4.61438 10.5 6.5C10.5 8.38562 10.5 9.32843 9.91421 9.91421C9.32843 10.5 8.38562 10.5 6.5 10.5C4.61438 10.5 3.67157 10.5 3.08579 9.91421C2.5 9.32843 2.5 8.38562 2.5 6.5Z"
                                        stroke="currentColor"
                                        strokeWidth="1.5"
                                    />
                                    <path
                                        opacity="0.5"
                                        d="M13.5 17.5C13.5 15.6144 13.5 14.6716 14.0858 14.0858C14.6716 13.5 15.6144 13.5 17.5 13.5C19.3856 13.5 20.3284 13.5 20.9142 14.0858C21.5 14.6716 21.5 15.6144 21.5 17.5C21.5 19.3856 21.5 20.3284 20.9142 20.9142C20.3284 21.5 19.3856 21.5 17.5 21.5C15.6144 21.5 14.6716 21.5 14.0858 20.9142C13.5 20.3284 13.5 19.3856 13.5 17.5Z"
                                        stroke="currentColor"
                                        strokeWidth="1.5"
                                    />
                                    <path
                                        d="M2.5 17.5C2.5 15.6144 2.5 14.6716 3.08579 14.0858C3.67157 13.5 4.61438 13.5 6.5 13.5C8.38562 13.5 9.32843 13.5 9.91421 14.0858C10.5 14.6716 10.5 15.6144 10.5 17.5C10.5 19.3856 10.5 20.3284 9.91421 20.9142C9.32843 21.5 8.38562 21.5 6.5 21.5C4.61438 21.5 3.67157 21.5 3.08579 20.9142C2.5 20.3284 2.5 19.3856 2.5 17.5Z"
                                        stroke="currentColor"
                                        strokeWidth="1.5"
                                    />
                                    <path
                                        d="M13.5 6.5C13.5 4.61438 13.5 3.67157 14.0858 3.08579C14.6716 2.5 15.6144 2.5 17.5 2.5C19.3856 2.5 20.3284 2.5 20.9142 3.08579C21.5 3.67157 21.5 4.61438 21.5 6.5C21.5 8.38562 21.5 9.32843 20.9142 9.91421C20.3284 10.5 19.3856 10.5 17.5 10.5C15.6144 10.5 14.6716 10.5 14.0858 9.91421C13.5 9.32843 13.5 8.38562 13.5 6.5Z"
                                        stroke="currentColor"
                                        strokeWidth="1.5"
                                    />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div className="flex gap-3">
                        
                        <select 
                            className="form-select" 
                            value={status} 
                            onChange={(e) => handleStatusChange(e.target.value)}
                        >
                            <option value="all">All</option>
                            <option value="pending">Approval Pending</option>
                            <option value="approved">Approved</option>
                            <option value="in_progress">In Progress</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                        <input 
                            type="date" 
                            className="form-input" 
                            value={date} 
                            onChange={(e) => handleDateChange(e.target.value)}
                        />
                        </div>
                    </div>
                    <div className="relative">
                    <input 
                        type="text" 
                        placeholder="Search Appointment" 
                        className="py-2 ltr:pr-11 rtl:pl-11 form-input peer" 
                        value={search} 
                        onChange={(e) => handleSearch(e.target.value)} 
                    />
                        <button type="button" className="top-1/2 ltr:right-[11px] rtl:left-[11px] absolute peer-focus:text-primary -translate-y-1/2">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="11.5" cy="11.5" r="9.5" stroke="currentColor" strokeWidth="1.5" opacity="0.5"></circle>
                                <path d="M18.5 18.5L22 22" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"></path>
                            </svg>
                        </button>
                    </div>
                </div>

            {value === 'list' && (
                <div className="mt-5 p-0 border-0 overflow-hidden panel">
                    {filteredItems.length > 0 ? (
                    <div className="table-responsive">
                        <table className="table-striped table-hover">
                            <thead>
                                <tr>
                                        <th>Ticket #</th>
                                        <th>Customer</th>
                                        <th>Date & Time</th>
                                        <th>Services</th>
                                    <th>Status</th>
                                    <th className="!text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                    {filteredItems.map((appointment: any) => {
                                    // Calculate total duration of all services
                                    const totalServicesDuration = appointment.services.reduce((sum: number, service: Service) => sum + service.duration_minutes, 0);
                                    // Calculate total price of all services
                                    const totalServicesPrice = appointment.services.reduce((sum: number, service: Service) => sum + Number(service.price), 0);

                                    return (
                                            <tr key={appointment.id}>
                                                <td>{appointment.ticket_number}</td>
                                            <td>
                                                <div className="flex items-center w-max">
                                                        <div className="place-content-center grid bg-primary ltr:mr-2 rtl:ml-2 rounded-full w-8 h-8 font-semibold text-sm text-white">
                                                            {appointment.name.charAt(0)}
                                                        </div>
                                                        <div>
                                                            <div>{appointment.name}</div>
                                                            <div className="text-xs text-gray-500">{appointment.email}</div>
                                                            {/* Staff info */}
                                                            {appointment.staff_json && (() => {
                                                                let staff: Staff | null = null;
                                                                try { staff = JSON.parse(appointment.staff_json); } catch {}
                                                                return staff ? (
                                                                    <div className="mt-1 text-xs text-blue-700 font-semibold">
                                                                        Staff: {staff.name} <br/>
                                                                        <span className="text-gray-500">{staff.email}</span> <br/>
                                                                        <span className="text-gray-500">{staff.phone}</span>
                                                                    </div>
                                                                ) : null;
                                                            })()}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="whitespace-nowrap">
                                                    <div>
                                                        {new Date(`${appointment.appointment_date} ${appointment.appointment_time}`).toLocaleString('en-US', {
                                                            year: 'numeric',
                                                            month: 'short',
                                                            day: 'numeric',
                                                            hour: 'numeric',
                                                            minute: '2-digit',
                                                            hour12: true
                                                        })}
                                                    </div>
                                                    {appointment.status === 'in_progress' && appointment.total_estimated_end_time && (
                                                        <div className="mt-1">
                                                            <CountdownTimer estimatedEndTime={appointment.total_estimated_end_time} />
                                                            <div className="text-xs text-gray-500">
                                                                Total Est. End: {new Date(appointment.total_estimated_end_time).toLocaleTimeString('en-US', {
                                                                    hour: 'numeric',
                                                                    minute: '2-digit',
                                                                    hour12: true
                                                                })}
                                                            </div>
                                                            {appointment.total_duration_minutes && (
                                                                <div className="text-xs text-gray-500">
                                                                    Total Duration: {formatDuration(appointment.total_duration_minutes)}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                </td>
                                                <td>
                                                    <div className="flex flex-wrap gap-1">

                                                        {appointment.appointmentServices.map((appoinmentService: any, idx: number) => (
                                                            <span key={appoinmentService.idx} className="badge badge-outline-primary">
                                                                {appoinmentService.appointment_service} - {appointment.currency_symbol}{appoinmentService.appointment_service_price}
                                                            </span>
                                                        ))}

                                                        {/* {appointment.services.map((service: Service) => (
                                                            <span key={service.id} className="badge badge-outline-primary">
                                                                {service.name}
                                                                {appointment.status === 'in_progress' && service.estimated_end_time && (
                                                                    <span className="ml-1 text-xs">
                                                                        ({formatDuration(service.duration_minutes)})
                                                                    </span>
                                                                )}
                                                            </span>
                                                        ))} */}
                                                    </div>
                                                    <div>
                                                    <div>
                                                        {appointment.appoinmentUsedPlan && appointment.appoinmentUsedPlan.length > 0 && (() => {
                                                            // Group services by plan name
                                                            const groupedPlans: { [key: string]: string[] } = {};
                                                            appointment.appoinmentUsedPlan.forEach((item: any) => {
                                                                if (!groupedPlans[item.name]) {
                                                                    groupedPlans[item.name] = [];
                                                                }
                                                                groupedPlans[item.name].push(item.service_name);
                                                            });

                                                            return (
                                                                <div className='ml-2 badge badge-outline-success text-left'>
                                                                    {Object.entries(groupedPlans).map(([planName, serviceNames], idx) => (
                                                                        <div key={idx} className="mb-1">
                                                                            <strong>{planName}</strong>
                                                                            <ul className="ml-2">
                                                                                {serviceNames.map((service, i) => (
                                                                                    <li key={i}>{service}</li>
                                                                                ))}
                                                                            </ul>
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            );
                                                        })()}
                                                    </div>
                                                    </div>
                                                    {/* <div className='my-2'>
                                                        {appointment.appointmentServices && appointment.appointmentServices.length > 0 && (
                                                            <div className='ml-2 badge badge-outline-info'>
                                                                {appointment.appointmentServices.map((appoinmentService: any, idx: number) => (
                                                                    <React.Fragment key={idx}>
                                                                        <div><span>{appoinmentService.appointment_service} : {appointment.currency_symbol}{appoinmentService.appointment_service_price}</span></div>
                                                                        {appoinmentService.appointment_service_notes && (
                                                                            <div><span>{appoinmentService.appointment_service_notes}</span></div>
                                                                        )}
                                                                    </React.Fragment>
                                                                ))}
                                                            </div>
                                                        )}
                                                    </div> */}

                                                    <div className="mt-1 text-xs font-semibold text-gray-700">

                                                        {/* Calculate discount and final total */}
                                                        {(() => {
                                                            const matchedServices = appointment.appoinmentUsedPlan.map(p => p.service_id);
                                                            const discountTotal = appointment.appointmentServices
                                                                .filter(s => matchedServices.includes(s.appointment_service_service_id))
                                                                .reduce((sum, s) => sum + Number(s.appointment_service_price || 0), 0);

                                                            const finalTotal = Number(appointment.app_price || 0) - discountTotal;

                                                            if (discountTotal > 0) {
                                                                return (
                                                                    <div className="text-sm text-gray-700 dark:text-gray-200 mt-2 space-y-1">
                                                                        <div>Total: {appointment.currency_symbol}{Number(appointment.app_price).toFixed(2)}</div>
                                                                        <div>Discount: {appointment.currency_symbol}{discountTotal.toFixed(2)}</div>
                                                                        <div className="font-semibold">Final: {appointment.currency_symbol}{finalTotal.toFixed(2)}</div>
                                                                    </div>
                                                                );
                                                            } else {
                                                                // No discount, show only app_price
                                                                return (
                                                                    <div className="text-sm text-gray-700 dark:text-gray-200 mt-2">
                                                                        Total Price: {appointment.currency_symbol}{Number(appointment.app_price).toFixed(2)}
                                                                    </div>
                                                                );
                                                            }
                                                        })()}

                                                        {/* Total Price: {appointment.currency_symbol}{appointment.app_price.toFixed(2)} */}


                                                        {(appointment.services[0]?.seat_name || appointment.services[0]?.staff_name) && (
                                                            <span className="block mt-1 text-xs text-gray-500">
                                                                {appointment.services[0]?.seat_name && <>Seat: {appointment.services[0].seat_name} </>}
                                                                {appointment.services[0]?.staff_name && <>Staff: {appointment.services[0].staff_name}</>}
                                                            </span>
                                                        )}
                                                    </div>

                                                </td>
                                                <td>
                                                    <div className="flex flex-col gap-2">
                                                        <div className="flex items-center">
                                                            <div className="flex-none ltr:mr-2 rtl:ml-2">Status :</div>

                                                            {(appointment.services[0]?.seat_name || appointment.services[0]?.staff_name) && (
                                                            <span className={`badge ${
                                                                appointment.status === 'confirmed' ? 'bg-success' :
                                                                appointment.status === 'pending' ? 'bg-success' :
                                                                appointment.status === 'completed' ? 'bg-info' :
                                                                'bg-danger'
                                                            }`}>
                                                                {appointment.status === 'pending' ? 'Approved' : formatStatus(appointment.status)}
                                                            </span>
                                                        )}

                                                        {(!appointment.services[0]?.seat_name && !appointment.services[0]?.staff_name) && (
                                                            <span className={`badge ${
                                                                appointment.status === 'confirmed' ? 'bg-success' :
                                                                appointment.status === 'pending' ? 'bg-warning' :
                                                                appointment.status === 'completed' ? 'bg-info' :
                                                                'bg-danger'
                                                            }`}>
                                                                {formatStatus(appointment.status)}
                                                            </span>
                                                        )}

                                                            {/* <span className={`badge ${
                                                                appointment.status === 'confirmed' ? 'bg-success' :
                                                                appointment.status === 'pending' ? 'bg-warning' :
                                                                appointment.status === 'completed' ? 'bg-info' :
                                                                'bg-danger'
                                                            }`}>
                                                                {formatStatus(appointment.status)}
                                                            </span> */}
                                                        </div>
                                                        <div className="flex items-center">
                                                            <div className="flex-none ltr:mr-2 rtl:ml-2">Duration :</div>
                                                            <span className="badge bg-primary/10 text-primary border border-primary/20 whitespace-nowrap">
                                                                <svg className="w-3 h-3 inline-block ltr:mr-1 rtl:ml-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M12 8V12L15 15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                                    <circle cx="12" cy="12" r="9" stroke="currentColor" strokeWidth="1.5"/>
                                                                </svg>
                                                                {formatDuration(totalServicesDuration)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </td>
                                            <td>
                                                <div className="flex justify-center items-center gap-4">
                                                        {(['completed', 'in_progress', 'cancelled'].includes(appointment.status)) ? null : (
                                                            <>
                                                                <Link href={`/vendor/appointments/${appointment.id}/edit`} className="btn btn-sm btn-outline-primary">
                                                                    Edit
                                                                </Link>
                                                                <button 
                                                                    type="button" 
                                                                    className="btn btn-sm btn-outline-danger"
                                                                    onClick={() => handleDelete(appointment)}
                                                                >
                                                                    Delete
                                                                </button>
                                                            </>
                                                        )}
                                                    </div>
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                        {/* Pagination Controls */}
                        {appointments.links && (
                            <div className="mt-8 mb-8 flex flex-wrap items-center justify-center gap-4">
                                {appointments.links.map((link: any, i: number) => (
                                    <Link
                                        key={i}
                                        href={link.url || '#'}
                                        className={`px-4 py-2 text-sm font-semibold rounded-md ${
                                            link.active
                                                ? 'bg-primary text-white'
                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                        dangerouslySetInnerHTML={{ __html: link.label === '&laquo; Previous' ? 'Previous' : link.label === 'Next &raquo;' ? 'Next' : link.label }}
                                    />
                                ))}
                            </div>
                        )}
                    </div>
                    ) : (
                        <div className="flex flex-col items-center justify-center p-8 text-center">
                            <div className="w-16 h-16 mb-4 text-gray-400">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                                {search || status || date ? 'No matching appointments found' : 'No appointments yet'}
                            </h3>
                            <p className="mt-2 text-gray-500 dark:text-gray-400">
                                {search || status || date 
                                    ? 'Try adjusting your search or filter criteria'
                                    : 'Start by creating your first appointment'}
                            </p>
                            {!search && !status && !date && (
                                <Link href="/vendor/appointments/create" className="mt-4 btn btn-primary">
                                    <svg className="ltr:mr-2 rtl:ml-2" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="10" cy="6" r="4" stroke="currentColor" strokeWidth="1.5" />
                                        <path opacity="0.5" d="M18 17.5C18 19.9853 18 22 10 22C2 22 2 19.9853 2 17.5C2 15.0147 5.58172 13 10 13C14.4183 13 18 15.0147 18 17.5Z" stroke="currentColor" strokeWidth="1.5" />
                                        <path d="M21 10H19M19 10H17M19 10L19 8M19 10L19 12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                    </svg>
                                    Add Appointment
                                </Link>
                            )}
                        </div>
                    )}
                </div>
            )}

            {value === 'grid' && (
                <div className="gap-6 grid grid-cols-1 sm:grid-cols-2 2xl:grid-cols-4 xl:grid-cols-3 mt-5 w-full">
                    {filteredItems.length > 0 ? (
                        <>
                        {filteredItems.map((appointment: any) => {
                        // Calculate total duration of all services
                        const totalServicesDuration = appointment.services.reduce((sum: number, service: Service) => sum + service.duration_minutes, 0);
                        // Calculate total price of all services
                        const totalServicesPrice = appointment.services.reduce((sum: number, service: Service) => sum + Number(service.price), 0);

                        return (
                                <div className="relative bg-white dark:bg-[#1c232f] shadow rounded-md text-center overflow-hidden" key={appointment.id}>
                                <div className="relative bg-white dark:bg-[#1c232f]  text-center overflow-hidden">
                                        <div className="bg-white/40 bg-cover bg-center p-6 pb-0 rounded-t-md">
                                            <div className="place-content-center grid bg-primary mx-auto rounded-full w-16 h-16 font-semibold text-xl text-white">
                                                {/* {appointment.name} */}
                                                <svg class="group-hover:!text-primary shrink-0" width="100" height="100" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle opacity="0.5" cx="15" cy="6" r="3" fill="currentColor"></circle><ellipse opacity="0.5" cx="16" cy="17" rx="5" ry="3" fill="currentColor"></ellipse><circle cx="9.00098" cy="6" r="4" fill="currentColor"></circle><ellipse cx="9.00098" cy="17.001" rx="7" ry="4" fill="currentColor"></ellipse></svg>
                                            </div>
                                    </div>
                                    <div className="relative -mt-10 px-6 pb-24">
                                        <div className="bg-white dark:bg-gray-900 shadow-md px-2 py-4 rounded-md">
                                                <div className="text-xl">{appointment.name}</div>
                                                <div className="text-white-dark">{appointment.ticket_number}</div>
                                                {/* Staff info */}
                                                {appointment.staff_json && (() => {
                                                    let staff: Staff | null = null;
                                                    try { staff = JSON.parse(appointment.staff_json); } catch {}
                                                    return staff ? (
                                                        <div className="mt-1 text-xs text-blue-700 font-semibold">
                                                            Staff: {staff.name} <br/>
                                                            <span className="text-gray-500">{staff.email}</span> <br/>
                                                            <span className="text-gray-500">{staff.phone}</span>
                                                        </div>
                                                    ) : null;
                                                })()}
                                                <div className="mt-4">
                                                    <div className="flex flex-wrap justify-center gap-2">
                                                        {/* {appointment.services.map((service: Service) => (
                                                            <span key={service.id} className="badge badge-outline-primary">
                                                                {service.name}
                                                                {appointment.status === 'in_progress' && service.estimated_end_time && (
                                                                    <span className="ml-1 text-xs">
                                                                        ({formatDuration(service.duration_minutes)})
                                                                    </span>
                                                                )}
                                                            </span>
                                                        ))} */}

                                                        {appointment.appointmentServices.map((appoinmentService: any, idx: number) => (
                                                            <span key={appoinmentService.idx} className="badge badge-outline-primary">
                                                                {appoinmentService.appointment_service} - {appointment.currency_symbol}{appoinmentService.appointment_service_price}
                                                            </span>
                                                        ))}

                                                        
                                                    </div>

                                                    <div>
                                                        {appointment.appoinmentUsedPlan && appointment.appoinmentUsedPlan.length > 0 && (() => {
                                                            // Group services by plan name
                                                            const groupedPlans: { [key: string]: string[] } = {};
                                                            appointment.appoinmentUsedPlan.forEach((item: any) => {
                                                                if (!groupedPlans[item.name]) {
                                                                    groupedPlans[item.name] = [];
                                                                }
                                                                groupedPlans[item.name].push(item.service_name);
                                                            });

                                                            return (
                                                                <div className='ml-2 badge badge-outline-success text-left'>
                                                                    {Object.entries(groupedPlans).map(([planName, serviceNames], idx) => (
                                                                        <div key={idx} className="mb-1">
                                                                            <strong>{planName}</strong>
                                                                            <ul className="ml-2">
                                                                                {serviceNames.map((service, i) => (
                                                                                    <li key={i}>{service}</li>
                                                                                ))}
                                                                            </ul>
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            );
                                                        })()}
                                                    </div>
                                                    {/* <div className='my-2'>
                                                        {appointment.appointmentServices && appointment.appointmentServices.length > 0 && (
                                                            <div className='ml-2 badge badge-outline-info'>
                                                                {appointment.appointmentServices.map((appoinmentService: any, idx: number) => (
                                                                    <React.Fragment key={idx}>
                                                                        <div><span>{appoinmentService.appointment_service} : {appointment.currency_symbol}{appoinmentService.appointment_service_price}</span></div>
                                                                        {appoinmentService.appointment_service_notes && (
                                                                            <div><span>{appoinmentService.appointment_service_notes}</span></div>
                                                                        )}
                                                                    </React.Fragment>
                                                                ))}
                                                            </div>
                                                        )}
                                                    </div> */}


                                                    <div className="mt-1 text-xs font-semibold text-gray-700">

                                                        {/* Calculate discount and final total */}
                                                        {(() => {
                                                            const matchedServices = appointment.appoinmentUsedPlan.map(p => p.service_id);
                                                            const discountTotal = appointment.appointmentServices
                                                                .filter(s => matchedServices.includes(s.appointment_service_service_id))
                                                                .reduce((sum, s) => sum + Number(s.appointment_service_price || 0), 0);

                                                            const finalTotal = Number(appointment.app_price || 0) - discountTotal;

                                                            if (discountTotal > 0) {
                                                                return (
                                                                    <div className="text-sm text-gray-700 dark:text-gray-200 mt-2 space-y-1">
                                                                        <div>Total: {appointment.currency_symbol}{Number(appointment.app_price).toFixed(2)}</div>
                                                                        <div>Discount: {appointment.currency_symbol}{discountTotal.toFixed(2)}</div>
                                                                        <div className="font-semibold">Final: {appointment.currency_symbol}{finalTotal.toFixed(2)}</div>
                                                                    </div>
                                                                );
                                                            } else {
                                                                // No discount, show only app_price
                                                                return (
                                                                    <div className="text-sm text-gray-700 dark:text-gray-200 mt-2">
                                                                        Total Price: {appointment.currency_symbol}{Number(appointment.app_price).toFixed(2)}
                                                                    </div>
                                                                );
                                                            }
                                                        })()}

                                                        {/* Total Price: {appointment.currency_symbol}{appointment.app_price.toFixed(2)} */}

                                                        {(appointment.services[0]?.seat_name || appointment.services[0]?.staff_name) && (
                                                            <span className="block mt-1 text-xs text-gray-500">
                                                                {appointment.services[0]?.seat_name && <>Seat: {appointment.services[0].seat_name} </>}
                                                                {appointment.services[0]?.staff_name && <>Staff: {appointment.services[0].staff_name}</>}
                                                            </span>
                                                        )}
                                                    </div>


                                                </div>
                                        </div>
                                        <div className="rtl:text-right gap-4 grid grid-cols-1 mt-6 ltr:text-left">
                                            <div className="flex items-center">
                                                <div className="flex-none ltr:mr-2 rtl:ml-2">Email :</div>
                                                    <div className="text-white-dark truncate">{appointment.email}</div>
                                            </div>
                                            <div className="flex items-center">
                                                <div className="flex-none ltr:mr-2 rtl:ml-2">Phone :</div>
                                                    <div className="text-white-dark">{appointment.phone}</div>
                                                </div>
                                                <div className="flex items-center">
                                                    <div className="flex-none ltr:mr-2 rtl:ml-2">Date & Time :</div>
                                                    <div className="text-white-dark">
                                                        {new Date(`${appointment.appointment_date} ${appointment.appointment_time}`).toLocaleString('en-US', {
                                                            year: 'numeric',
                                                            month: 'short',
                                                            day: 'numeric',
                                                            hour: 'numeric',
                                                            minute: '2-digit',
                                                            hour12: true
                                                        })}
                                                        {appointment.status === 'in_progress' && appointment.total_estimated_end_time && (
                                                            <div className="mt-1">
                                                                <CountdownTimer estimatedEndTime={appointment.total_estimated_end_time} />
                                                                <div className="text-xs text-gray-500">
                                                                    Total Est. End: {new Date(appointment.total_estimated_end_time).toLocaleTimeString('en-US', {
                                                                        hour: 'numeric',
                                                                        minute: '2-digit',
                                                                        hour12: true
                                                                    })}
                                                                </div>
                                                                {appointment.total_duration_minutes && (
                                                                    <div className="text-xs text-gray-500">
                                                                        Total Duration: {formatDuration(appointment.total_duration_minutes)}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                    </div>
                                            </div>
                                            <div className="flex items-center">
                                                    <div className="flex-none ltr:mr-2 rtl:ml-2">Status :</div>
                                                    {(appointment.services[0]?.seat_name || appointment.services[0]?.staff_name) && (
                                                            <span className={`badge ${
                                                                appointment.status === 'confirmed' ? 'bg-success' :
                                                                appointment.status === 'pending' ? 'bg-success' :
                                                                appointment.status === 'completed' ? 'bg-info' :
                                                                'bg-danger'
                                                            }`}>
                                                                {appointment.status === 'pending' ? 'Approved' : formatStatus(appointment.status)}
                                                            </span>
                                                        )}

                                                        {(!appointment.services[0]?.seat_name && !appointment.services[0]?.staff_name) && (
                                                            <span className={`badge ${
                                                                appointment.status === 'confirmed' ? 'bg-success' :
                                                                appointment.status === 'pending' ? 'bg-warning' :
                                                                appointment.status === 'completed' ? 'bg-info' :
                                                                'bg-danger'
                                                            }`}>
                                                                {formatStatus(appointment.status)}
                                                            </span>
                                                        )}
                                                        
                                                </div>
                                            <div className="flex items-center">
                                                    <div className="flex-none ltr:mr-2 rtl:ml-2">Duration :</div>
                                                    <span className="badge bg-primary/10 text-primary border border-primary/20 whitespace-nowrap">
                                                        <svg className="w-3 h-3 inline-block ltr:mr-1 rtl:ml-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M12 8V12L15 15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                            <circle cx="12" cy="12" r="9" stroke="currentColor" strokeWidth="1.5"/>
                                                        </svg>
                                                        {formatDuration(totalServicesDuration)}
                                                    </span>
                                                </div>
                                        </div>
                                    </div>
                                    {(['completed', 'in_progress', 'cancelled'].includes(appointment.status)) ? null : (
                                        <>
                                         <div className="rtl:right-0 bottom-0 ltr:left-0 absolute flex gap-4 mt-6 p-6 w-full">
                                            <Link href={`/vendor/appointments/${appointment.id}/edit`} className="w-1/2 btn btn-outline-primary">
                                                Edit
                                            </Link>
                                                <button 
                                                    type="button" 
                                                    className="w-1/2 btn btn-outline-danger"
                                                    onClick={() => handleDelete(appointment)}
                                                >
                                                Delete
                                            </button>
                                        </div>
                                        </>
                                    )}
                                   
                                </div>
                            </div>
                        );
                        })}
                        {/* Pagination Controls for Grid View */}
                        {appointments.links && (
                            <div className="col-span-full mt-8 mb-8 flex flex-wrap items-center justify-center gap-4">
                                {appointments.links.map((link: any, i: number) => (
                                    <Link
                                        key={i}
                                        href={link.url || '#'}
                                        className={`px-4 py-2 text-sm font-semibold rounded-md ${
                                            link.active
                                                ? 'bg-primary text-white'
                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                        dangerouslySetInnerHTML={{ __html: link.label === '&laquo; Previous' ? 'Previous' : link.label === 'Next &raquo;' ? 'Next' : link.label }}
                                    />
                                ))}
                            </div>
                        )}
                        </>
                    ) : (
                        <div className="col-span-full">
                            <div className="flex flex-col items-center justify-center p-8 text-center">
                                <div className="w-16 h-16 mb-4 text-gray-400">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
                                    </svg>
                </div>
                                <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                                    {search || status || date ? 'No matching appointments found' : 'No appointments yet'}
                                </h3>
                                <p className="mt-2 text-gray-500 dark:text-gray-400">
                                    {search || status || date 
                                        ? 'Try adjusting your search or filter criteria'
                                        : 'Start by creating your first appointment'}
                                </p>
                                {!search && !status && !date && (
                                    <Link href="/vendor/appointments/create" className="mt-4 btn btn-primary">
                                        <svg className="ltr:mr-2 rtl:ml-2" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="10" cy="6" r="4" stroke="currentColor" strokeWidth="1.5" />
                                            <path opacity="0.5" d="M18 17.5C18 19.9853 18 22 10 22C2 22 2 19.9853 2 17.5C2 15.0147 5.58172 13 10 13C14.4183 13 18 15.0147 18 17.5Z" stroke="currentColor" strokeWidth="1.5" />
                                            <path d="M21 10H19M19 10H17M19 10L19 8M19 10L19 12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                        </svg>
                                        Add Appointment
                                    </Link>
                                )}
                                    </div>
                        </div>
                    )}
                    </div>
            )}
        </div>
    );
};

export default Appointment;
