import React, { useEffect, useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import Swal from 'sweetalert2';
import { usePage } from '@inertiajs/react';
import Flatpickr from 'react-flatpickr';
import 'flatpickr/dist/flatpickr.css';

interface Service {
    id: number;
    name: string;
    duration_minutes: number;
    price: number;
}

interface AppointmentService {
    id: number;
    name: string;
    duration_minutes: number;
    price: number;
    seat_id?: number;
}

interface Customer {
    id: number;
    name: string;
    email: string;
    phone: string;
}

interface Seat {
    id: number;
    name: string;
}

interface Staff {
    id: number;
    name: string;
    email: string;
    phone: string;
}

interface PageProps {
    services: Service[];
    customers: Customer[];
    allSeats: Seat[];
    appointment: {
        id: number;
        user_id: number;
        currency_symbol: string;
        currency_text: string;
        appointment_date: string;
        appointment_time: string;
        ticket_number: string;
        status: string;
        notes: string;
        services: AppointmentService[];
        staff_id?: number;
    };
    staff?: Staff[];
    [key: string]: any;
}

const EditAppointment = () => {
    const dispatch = useDispatch();
    const { services, customers, appointment, allSeats, staff = [] } = usePage<PageProps & { staff?: Staff[] }>().props;

    // Working hours state
    const [workingHours, setWorkingHours] = useState<any[]>([]);
    const [selectedDay, setSelectedDay] = useState<string>('');
    const [availableTimes, setAvailableTimes] = useState<string[]>([]);
    const [dayClosed, setDayClosed] = useState<boolean>(false);
    const [workingHourError, setWorkingHourError] = useState<string>('');

    useEffect(() => {
        dispatch(setPageTitle('Edit Appointment'));
        // Fetch working hours for the current branch
        fetch('/get-working-hours')
            .then(res => res.json())
            .then(data => {
                setWorkingHours(data.workingHours || []);
            });
    }, [dispatch]);

    // Helper to get day name from date string (YYYY-MM-DD)
    const getDayName = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { weekday: 'long' });
    };

    // Helper to generate time slots between open and close
    const generateTimeSlots = (open: string, close: string, interval = 15) => {
        const slots: string[] = [];
        let [openHour, openMinute] = open.split(':').map(Number);
        let [closeHour, closeMinute] = close.split(':').map(Number);
        let start = new Date();
        start.setHours(openHour, openMinute, 0, 0);
        let end = new Date();
        end.setHours(closeHour, closeMinute, 0, 0);
        while (start <= end) {
            slots.push(start.toTimeString().slice(0, 5));
            start = new Date(start.getTime() + interval * 60000);
        }
        return slots;
    };

    // Update available times and closed status when appointment_date changes or workingHours loaded
    useEffect(() => {
        if (!appointment.appointment_date || workingHours.length === 0) return;
        const dayName = getDayName(appointment.appointment_date);
        setSelectedDay(dayName);
        const dayWorkingHour = workingHours.find((wh) => wh.day === dayName);
        if (dayWorkingHour) {
            if (dayWorkingHour.is_closed) {
                setDayClosed(true);
                setWorkingHourError(`The branch is closed on ${dayName}. You cannot edit appointments for this day.`);
                setAvailableTimes([]);
            } else {
                setDayClosed(false);
                setWorkingHourError('');
                setAvailableTimes(generateTimeSlots(dayWorkingHour.open, dayWorkingHour.close));
            }
        } else {
            setDayClosed(true);
            setWorkingHourError(`No working hours found for ${dayName}.`);
            setAvailableTimes([]);
        }
    }, [appointment.appointment_date, workingHours]);

    // When user changes date, update available times
    const handleDateChange = (date: Date[]) => {
        if (date[0]) {
            const year = date[0].getFullYear();
            const month = String(date[0].getMonth() + 1).padStart(2, '0');
            const day = String(date[0].getDate()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day}`;
            // Update appointment date in formik
            setFieldValueGlobal('appointment_date', formattedDate);
            // Update available times and closed status
            const dayName = getDayName(formattedDate);
            setSelectedDay(dayName);
            const dayWorkingHour = workingHours.find((wh) => wh.day === dayName);
            if (dayWorkingHour) {
                if (dayWorkingHour.is_closed) {
                    setDayClosed(true);
                    setWorkingHourError(`The branch is closed on ${dayName}. You cannot edit appointments for this day.`);
                    setAvailableTimes([]);
                } else {
                    setDayClosed(false);
                    setWorkingHourError('');
                    setAvailableTimes(generateTimeSlots(dayWorkingHour.open, dayWorkingHour.close));
                }
            } else {
                setDayClosed(true);
                setWorkingHourError(`No working hours found for ${dayName}.`);
                setAvailableTimes([]);
            }
        }
    };

    // To allow handleDateChange to set Formik value
    let setFieldValueGlobal: any = () => {};

    const validationSchema = Yup.object().shape({
        user_id: Yup.number().required('Please select a customer'),
        appointment_date: Yup.string()
            .required('Please select an appointment date')
            .test('is-valid-date', 'Appointment date must be today or later', function(value) {
                if (!value) return false;
                const [year, month, day] = value.split('-').map(Number);
                const selectedDate = new Date(year, month - 1, day);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                return selectedDate >= today;
            }),
        appointment_time: Yup.string().required('Please select an appointment time'),
        services: Yup.array()
            .of(
                Yup.object().shape({
                    id: Yup.number().required(),
                    name: Yup.string().required(),
                    price: Yup.number().required(),
                })
            )
            .min(1, 'Please select at least one service')
            .required('Please select at least one service'),
        notes: Yup.string().max(500, 'Notes cannot exceed 500 characters'),
        status: Yup.string()
            .oneOf(['pending', 'completed', 'cancelled','in_progress'], 'Invalid status')
            .required('Please select a status'),
        seat_id: Yup.number().when('status', {
            is: (status: string) => ['pending', 'completed', 'cancelled'].includes(status),
            then: (schema) => schema.required('Please select a seat'),
            otherwise: (schema) => schema,
        }),
    });

    const initialValues = {
        user_id: appointment.user_id,
        currency_symbol: appointment.currency_symbol,
        currency_text: appointment.currency_text,
        appointment_date: appointment.appointment_date,
        appointment_time: appointment.appointment_time,
        services: appointment.services,
        notes: appointment.notes || '',
        status: appointment.status,
        seat_id: appointment.services[0]?.seat_id || '',
        staff_id: appointment.staff_id || '',
    };

    console.log(initialValues);

    const handleSubmit = (values: any) => {
        router.put(`/vendor/appointments/${appointment.id}`, values, {
            onSuccess: () => {
                Swal.fire({
                    icon: 'success',
                    title: 'Appointment updated successfully',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                });
            },
            onError: (errors) => {
                    const errorValues = Object.values(errors);
                    const firstError =
                    typeof errorValues[0] === 'string'
                        ? errorValues[0]
                        : errorValues[0]?.[0] || 'Error updating appointment';

                    Swal.fire({
                    icon: 'error',
                    title: firstError,
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 4000,
                    });
            },
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/appointments" className="text-primary hover:underline">
                        Appointments
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Edit Appointment</span>
                </li>
            </ul>

            <div className="panel mt-6">
                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                    enableReinitialize
                >
                    {({ errors, touched, values, setFieldValue }) => {
                        setFieldValueGlobal = setFieldValue;
                        return (
                        <Form className="space-y-5">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                {/* Customer Selection */}
                                <div>
                                    <label htmlFor="user_id">Customer</label>
                                    <select
                                        id="user_id"
                                        name="user_id"
                                        className="form-select"
                                        value={values.user_id}
                                        onChange={(e) => setFieldValue('user_id', e.target.value)}
                                    >
                                        <option value="">Select Customer</option>
                                        {customers.map((customer) => (
                                            <option key={customer.id} value={customer.id}>
                                                {customer.name} ({customer.email})
                                            </option>
                                        ))}
                                    </select>
                                    {errors.user_id && touched.user_id && (
                                        <div className="text-danger mt-1">{errors.user_id}</div>
                                    )}
                                </div>

                                {/* Appointment Date */}
                                <div>
                                    <label htmlFor="appointment_date">Appointment Date
                                        {(!appointment.appointment_date ||
                                          new Date(appointment.appointment_date) < new Date(new Date().toDateString())) && (
                                            <span className="text-danger mx-2 mb-1">
                                                 ( Past Appoinment Date : <b>{appointment.appointment_date || 'Missing'}</b> )
                                            </span>
                                        )}
                                    </label>
                                    <Flatpickr
                                        id="appointment_date"
                                        name="appointment_date"
                                        className="form-input"
                                        value={values.appointment_date}
                                        onChange={handleDateChange}
                                        options={{
                                            dateFormat: 'Y-m-d',
                                            minDate: 'today',
                                            disableMobile: true,
                                            time_24hr: true,
                                            enableTime: false,
                                        }}
                                    />
                                    {errors.appointment_date && touched.appointment_date && (
                                        <div className="text-danger mt-1">{errors.appointment_date}</div>
                                    )}
                                </div>

                                {/* Appointment Time (Dropdown) */}
                                <div>
                                    <label htmlFor="appointment_time">Appointment Time</label>
                                    <Flatpickr
                                        id="appointment_time"
                                        name="appointment_time"
                                        className="form-input"
                                        value={values.appointment_time}
                                        onChange={(time) => {
                                            if (time[0]) {
                                                setFieldValue('appointment_time', time[0].toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true }));
                                            }
                                        }}
                                        options={{
                                            enableTime: true,
                                            noCalendar: true,
                                            dateFormat: 'h:i K',
                                            time_24hr: false,
                                            disableMobile: true,
                                            minTime: availableTimes[0] ? availableTimes[0].split(':')[0] : '',
                                            maxTime: availableTimes[availableTimes.length - 1] ? availableTimes[availableTimes.length - 1].split(':')[0] : '',
                                            minuteIncrement: 15,
                                        }}
                                        disabled={dayClosed}
                                    />
                                    {errors.appointment_time && touched.appointment_time && (
                                        <div className="text-danger mt-1">{errors.appointment_time}</div>
                                    )}
                                </div>

                                {/* Show error if day is closed */}
                                {workingHourError && (
                                    <div className="text-danger mb-2">{workingHourError}</div>
                                )}

                                {/* Services Selection */}
                                <div className="md:col-span-2">
                                    <label>Services</label>
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                                        {services.map((service) => {
                                            // Find if this service was in the original appointment
                                            const originalService = appointment.services.find(s => s.id === service.id);
                                            const displayName = originalService ? originalService.name : service.name;
                                            const displayPrice = originalService ? originalService.price : service.price;
                                            
                                            return (
                                                <div key={service.id} className="flex items-center">
                                                    <input
                                                        type="checkbox"
                                                        id={`service-${service.id}`}
                                                        className="form-checkbox"
                                                        checked={values.services.some((s) => s.id === service.id)}
                                                        onChange={(e) => {
                                                            const newServices = e.target.checked
                                                                ? [...values.services, {
                                                                    id: service.id,
                                                                    name: displayName,
                                                                    price: displayPrice,
                                                                    duration_minutes: service.duration_minutes
                                                                  }]
                                                                : values.services.filter((s) => s.id !== service.id);
                                                            setFieldValue('services', newServices);
                                                        }}
                                                    />
                                                    <label htmlFor={`service-${service.id}`} className="ml-2">
                                                        {displayName} - {values.currency_symbol}{displayPrice}
                                                    </label>
                                                </div>
                                            );
                                        })}
                                    </div>
                                    {errors.services && touched.services && (
                                        <div className="text-danger mt-1">
                                            {typeof errors.services === 'string' ? errors.services : 'Please select at least one service'}
                                        </div>
                                    )}
                                </div>

                                {/* Status Selection */}
                                <div>
                                    <label>Status</label>
                                    {values.status === 'in_progress' ? (
                                        <>
                                            <div className="mb-2 p-2 bg-blue-100 text-blue-800 rounded">
                                                This appointment service is currently in progress.
                                            </div>
                                            <input type="hidden" name="status" value="in_progress" />
                                        </>
                                    ) : (
                                        <div className="flex gap-4 mt-2">
                                            <label className="flex items-center">
                                                <input
                                                    type="radio"
                                                    name="status"
                                                    value="pending"
                                                    checked={values.status === 'pending'}
                                                    onChange={() => setFieldValue('status', 'pending')}
                                                    className="form-radio"
                                                />
                                                <span className="ml-2">Approve</span>
                                            </label>
                                            <label className="flex items-center">
                                                <input
                                                    type="radio"
                                                    name="status"
                                                    value="completed"
                                                    checked={values.status === 'completed'}
                                                    onChange={() => setFieldValue('status', 'completed')}
                                                    className="form-radio"
                                                />
                                                <span className="ml-2">Completed</span>
                                            </label>
                                            <label className="flex items-center">
                                                <input
                                                    type="radio"
                                                    name="status"
                                                    value="cancelled"
                                                    checked={values.status === 'cancelled'}
                                                    onChange={() => setFieldValue('status', 'cancelled')}
                                                    className="form-radio"
                                                />
                                                <span className="ml-2">Cancel</span>
                                            </label>
                                        </div>
                                    )}
                                    {errors.status && touched.status && (
                                        <div className="text-danger mt-1">{errors.status}</div>
                                    )}
                                </div>

                                {/* Seat Selection */}
                                <div>
                                    <label htmlFor="seat_id">Select Seat</label>
                                    <select
                                        id="seat_id"
                                        name="seat_id"
                                        className="form-select"
                                        value={values.seat_id}
                                        onChange={(e) => setFieldValue('seat_id', e.target.value)}
                                    >
                                        <option value="">Select Seat</option>
                                        {allSeats.map((seat) => (
                                            <option key={seat.id} value={seat.id}>
                                                {seat.name}
                                            </option>
                                        ))}
                                    </select>
                                    {errors.seat_id && touched.seat_id && (
                                        <div className="text-danger mt-1">{errors.seat_id}</div>
                                    )}
                                </div>

                                {/* Staff Selection */}
                                {staff.length > 0 && (
                                    <div>
                                        <label htmlFor="staff_id">Select Staff</label>
                                        <select
                                            id="staff_id"
                                            name="staff_id"
                                            className="form-select"
                                            value={values.staff_id}
                                            onChange={(e) => setFieldValue('staff_id', e.target.value)}
                                        >
                                            <option value="">Select Staff</option>
                                            {staff.map((s) => (
                                                <option key={s.id} value={s.id}>
                                                    {s.name} ({s.email})
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                )}

                                {/* Notes */}
                                <div className="md:col-span-2">
                                    <label htmlFor="notes">Notes</label>
                                    <textarea
                                        id="notes"
                                        name="notes"
                                        className="form-textarea"
                                        rows={4}
                                        value={values.notes}
                                        onChange={(e) => setFieldValue('notes', e.target.value)}
                                    ></textarea>
                                    {errors.notes && touched.notes && (
                                        <div className="text-danger mt-1">{errors.notes}</div>
                                    )}
                                </div>
                            </div>

                            <div className="flex justify-end gap-4 mt-6">
                                <Link href="/vendor/appointments" className="btn btn-outline-danger">
                                    Cancel
                                </Link>
                                <button type="submit" className="btn btn-primary" disabled={dayClosed}>
                                    Update Appointment
                                </button>
                            </div>
                        </Form>
                        );
                    }}
                </Formik>
            </div>
        </div>
    );
};

export default EditAppointment; 