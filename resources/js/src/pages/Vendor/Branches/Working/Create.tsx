import React, { useEffect } from 'react';
import { Link, useForm } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';

interface WorkingHour {
    day: string;
    open: string;
    close: string;
    is_closed: boolean;
}

interface Props {
    workingHours: WorkingHour[];
}

const Create = ({ workingHours }: Props) => {
    const dispatch = useDispatch();
    const { data, setData, post, processing, errors } = useForm<{ working_hours: WorkingHour[] }>({
        working_hours: workingHours || [],
    });

    useEffect(() => {
        dispatch(setPageTitle('Set Branch Working Hours'));
    }, [dispatch]);

    const handleChange = (idx: number, field: keyof WorkingHour, value: string | boolean) => {
        const updated = [...(data.working_hours || [])];
        updated[idx] = { ...updated[idx], [field]: value };
        setData('working_hours', updated);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/vendor/branches/working-hours', {
            onSuccess: () => {},
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/branches" className="text-primary hover:underline">
                        Branches
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/branches/working" className="text-primary hover:underline">
                        Working Hours
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Set</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Set Branch Working Hours</h5>
                    </div>
                    <form className="space-y-5" onSubmit={handleSubmit}>
                        <div className="table-responsive">
                            <table className="table-striped">
                                <thead>
                                    <tr>
                                        <th>Day</th>
                                        <th>Open</th>
                                        <th>Close</th>
                                        <th>Closed</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {(data.working_hours || []).map((wh: WorkingHour, idx: number) => (
                                        <tr key={wh.day}>
                                            <td>{wh.day}</td>
                                            <td>
                                                <input
                                                    type="time"
                                                    className="form-input"
                                                    value={wh.open}
                                                    disabled={wh.is_closed}
                                                    onChange={e => handleChange(idx, 'open', e.target.value)}
                                                />
                                            </td>
                                            <td>
                                                <input
                                                    type="time"
                                                    className="form-input"
                                                    value={wh.close}
                                                    disabled={wh.is_closed}
                                                    onChange={e => handleChange(idx, 'close', e.target.value)}
                                                />
                                            </td>
                                            <td>
                                                <input
                                                    type="checkbox"
                                                    className="form-checkbox"
                                                    checked={wh.is_closed}
                                                    onChange={e => handleChange(idx, 'is_closed', e.target.checked)}
                                                />
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                        {errors.working_hours && (
                            <div className="text-danger mt-1">{errors.working_hours as string}</div>
                        )}
                        <div className="flex items-center justify-end gap-4 mt-4">
                            <Link href={route('vendor.branches.working-hours.index')} className="btn btn-outline-danger">
                                Cancel
                            </Link>
                            <button type="submit" className="btn btn-primary" disabled={processing}>
                                {processing ? 'Saving...' : 'Save Working Hours'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Create; 