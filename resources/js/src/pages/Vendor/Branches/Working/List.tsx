import React, { useEffect } from 'react';
import { Link } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@store/themeConfigSlice';

interface WorkingHour {
    day: string;
    open: string;
    close: string;
    is_closed: boolean;
}

interface Props {
    workingHours: WorkingHour[];
}

const List = ({ workingHours }: Props) => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Branch Working Hours'));
    }, [dispatch]);

    const safeWorkingHours = Array.isArray(workingHours) ? workingHours : [];

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/branches" className="text-primary hover:underline">
                        Branches
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Working Hours</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="flex items-center justify-between mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Branch Working Hours</h5>
                        <Link href={route('vendor.branches.working-hours.save')} className="btn btn-primary">
                                Set Working Hours
                        </Link>
                    </div>
                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th>Day</th>
                                    <th>Open</th>
                                    <th>Close</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {safeWorkingHours.length === 0 ? (
                                    <tr>
                                        <td colSpan={4} className="text-center text-gray-400 py-8">
                                            No working hours set. Please click 'Set Working Hours' to add timings for each day.
                                        </td>
                                    </tr>
                                ) : (
                                    safeWorkingHours.map((wh) => (
                                        <tr key={wh.day}>
                                            <td>{wh.day}</td>
                                            <td>{wh.is_closed ? '-' : wh.open}</td>
                                            <td>{wh.is_closed ? '-' : wh.close}</td>
                                            <td>
                                                {wh.is_closed ? (
                                                    <span className="badge badge-outline-danger">Closed</span>
                                                ) : (
                                                    <span className="badge badge-outline-success">Open</span>
                                                )}
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default List;
