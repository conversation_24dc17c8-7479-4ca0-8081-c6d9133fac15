/* Modern Salon Frontend Styles */

/* Custom Scrollbar */
.scrollbar-thin {
    scrollbar-width: thin;
}

.scrollbar-thumb-primary\/20 {
    scrollbar-color: rgba(252, 150, 0, 0.2) transparent;
}

.scrollbar-track-transparent {
    scrollbar-track-color: transparent;
}

/* Webkit Scrollbar for Chrome/Safari */
.scrollbar-thin::-webkit-scrollbar {
    width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(252, 150, 0, 0.2);
    border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(252, 150, 0, 0.4);
}

/* Glass morphism effects */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Floating animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-float-delayed {
    animation: float 3s ease-in-out infinite;
    animation-delay: 1.5s;
}

/* Hero Background Animations */
@keyframes float-slow {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
    }
    33% {
        transform: translateY(-15px) translateX(10px);
    }
    66% {
        transform: translateY(5px) translateX(-5px);
    }
}

@keyframes float-medium {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
    }
    50% {
        transform: translateY(-20px) translateX(15px);
    }
}

@keyframes float-fast {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
    }
    25% {
        transform: translateY(-10px) translateX(-8px);
    }
    75% {
        transform: translateY(8px) translateX(12px);
    }
}

@keyframes rotate-slow {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes rotate-reverse {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(-360deg);
    }
}

.animate-float-slow {
    animation: float-slow 8s ease-in-out infinite;
}

.animate-float-medium {
    animation: float-medium 6s ease-in-out infinite;
}

.animate-float-fast {
    animation: float-fast 4s ease-in-out infinite;
}

.animate-rotate-slow {
    animation: rotate-slow 20s linear infinite;
}

.animate-rotate-reverse {
    animation: rotate-reverse 15s linear infinite;
}

/* Gradient text animations */
@keyframes gradient-shift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
}

/* Pulse glow effect */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(252, 150, 0, 0.3);
    }
    50% {
        box-shadow: 0 0 40px rgba(252, 150, 0, 0.6);
    }
}

.animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
}

/* Mobile-first responsive utilities */
@media (max-width: 640px) {
    .mobile-full-width {
        width: 100vw;
        margin-left: calc(-50vw + 50%);
    }

    .mobile-padding {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .mobile-text-center {
        text-align: center;
    }

    .mobile-hidden {
        display: none;
    }
}

/* Enhanced button hover effects */
.btn-modern {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover::before {
    left: 100%;
}

/* Card hover effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Text line clamp utility */
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Custom focus styles */
.focus-modern:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(154, 52, 18, 0.3);
    border-color: #9a3412;
}

/* Loading spinner */
@keyframes spin-slow {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.animate-spin-slow {
    animation: spin-slow 2s linear infinite;
}

/* Stagger animation for lists */
.stagger-animation > * {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced mobile touch targets */
@media (max-width: 768px) {
    .touch-target {
        min-height: 44px;
        min-width: 44px;
    }

    .mobile-sticky-bottom {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 50;
    }
}

/* Improved form inputs */
.form-input-modern {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.form-input-modern:focus {
    border-color: #9a3412;
    box-shadow: 0 0 0 3px rgba(154, 52, 18, 0.1);
    transform: translateY(-2px);
}

/* Service card image overlay */
.service-image-overlay {
    position: absolute;
    inset: 0;
    background: rgba(154, 52, 18, 0.8);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.service-card:hover .service-image-overlay {
    opacity: 1;
}

/* Notification styles */
.notification-success {
    background: #00ab55;
    color: white;
    padding: 1rem;
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 171, 85, 0.3);
}

.notification-error {
    background: #e7515a;
    color: white;
    padding: 1rem;
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(231, 81, 90, 0.3);
}

/* Enhanced mobile modal */
@media (max-width: 640px) {
    .mobile-modal {
        margin: 0;
        width: 100vw;
        height: 100vh;
        max-width: none;
        max-height: none;
        border-radius: 0;
    }

    .mobile-modal-content {
        height: 100vh;
        overflow-y: auto;
        padding: 1rem;
    }
}
