import React from 'react';
import ReactDOMServer from 'react-dom/server';
import { createInertiaApp } from '@inertiajs/react';
import createServer from '@inertiajs/react/server';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';

// Redux
import { Provider } from 'react-redux';
import store from './src/store/index';
import InertiaLayout from './src/layouts/InertiaLayout';

createServer((page) =>
  createInertiaApp({
    page,
    render: ReactDOMServer.renderToString,
    resolve: (name) => {
      const pages = import.meta.glob('./src/pages/**/*.tsx', { eager: true });
      const page = pages[`./src/pages/${name}.tsx`];
      return page;
    },
    setup: ({ App, props }) => {
      return (
        <Provider store={store}>
          <App {...props}>
            {({ Component, props }) => (
              <InertiaLayout layout={props.layout}>
                <Component {...props} />
              </InertiaLayout>
            )}
          </App>
        </Provider>
      );
    },
  })
);
